{"sourceFile": "services/app/public/librerias/funciones_aws.php", "activeCommit": 0, "commits": [{"activePatchIndex": 11, "patches": [{"date": 1732072312629, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1732111272530, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -191,8 +191,13 @@\n         } else {\n             $remitente_mail = $remitente_nombre = $remitente;\n         }\n \n+        if ($remitente_mail == '<EMAIL>') {\n+            mostrar_error('Intento de <NAME_EMAIL> con el texto:<br>'.$texto, true);\n+            return true;\n+        }\n+\n         $messageAttributes = [\n             \"Subject\" => [\n                 'DataType' => \"String\",\n                 'StringValue' => $asunto\n"}, {"date": 1748888163904, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,279 @@\n+<?php\n+\n+use Aws\\Common\\Aws;\n+use Guzzle\\Http\\EntityBody;\n+\n+function conectar_aws($servicio)\n+{\n+    try {\n+        switch ($servicio) {\n+            case 's3':\n+                $aws = Aws::factory([\n+                    'key' => AWS_KEY,\n+                    'secret' => AWS_SECRET,\n+                    'region' => AWS_REGION])\n+                    ->get('s3');\n+                break;\n+\n+            case 'sqs':\n+                $aws = Aws::factory([\n+                    'key' => AWS_SQS_QUEUER_KEY,\n+                    'secret' => AWS_SQS_QUEUER_SECRET,\n+                    'region' => AWS_REGION])\n+                    ->get('sqs');\n+                break;\n+        }\n+        return $aws;\n+\n+    } catch (Exception $e) {\n+        mostrar_error('Error al conectarse con AWS '.$servicio, true);\n+    }\n+}\n+\n+function archivos_baja($ids = array())\n+{\n+    global $modulo;\n+    global $id;\n+\n+    if (!AWS_KEY)\n+        return true;\n+\n+    $s3 = conectar_aws('s3');\n+\n+    if (count($ids)) {\n+        $str_ids = '';\n+        foreach ($ids as $value)\n+            $str_ids.= \"'$value',\";\n+        $str_ids[mb_strlen($str_ids) - 1] = ' ';\n+        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE idarchivo IN ($str_ids)\");\n+\n+    } else {\n+        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE modulo='\".$modulo.\"' AND id='\".$id.\"'\");\n+    }\n+\n+    while ($archivo = array_sql($resultado_sql)) {\n+\n+        if (!$_SESSION['perfil_archivos_baja_todos'] && $archivo['idusuario'] != $_SESSION['usuario_idusuario'])\n+            return false;\n+\n+        $objetos = array();\n+        if ($archivo['tipo'] == 'imagen') {\n+            $objetos[] = array('Key' => $archivo['iua'].'/imagen');\n+            $objetos[] = array('Key' => $archivo['iua'].'/miniatura');\n+        }\n+        $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);\n+        $objetos[] = array('Key' => $archivo['iua']);\n+\n+        if (!$s3->deleteObjects(array('Bucket' => AWS_BUCKET, 'Objects' => $objetos)))\n+            return false;\n+\n+    }\n+\n+    if (count($ids))\n+        consulta_sql(\"DELETE FROM archivos WHERE idarchivo IN ($str_ids)\");\n+    else\n+        consulta_sql(\"DELETE FROM archivos WHERE modulo = '$modulo' AND id = '$id'\");\n+\n+    return true;\n+}\n+\n+function existe_wsfe($cuit, $extension)\n+{\n+    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+\n+    if (is_readable($local_file))\n+        return true;\n+\n+    if (ESTADO == 'desarrollo' && AWS_SECRET == '')\n+        return false;\n+\n+    $s3 = conectar_aws('s3');\n+    if (!$s3->doesObjectExist(AWS_WSFE, $cuit.'/'.$cuit.'.'.$extension))\n+        return false;\n+\n+    descargar_wsfe($cuit, $extension);\n+    return true;\n+}\n+\n+function descargar_wsfe($cuit, $unica_extension = false)\n+{\n+    $s3 = conectar_aws('s3');\n+    $extensiones = array('key', 'req', 'ini', 'crt');\n+\n+    foreach ($extensiones as $extension) {\n+        if (!$unica_extension || $unica_extension == $extension) {\n+\n+            $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+            $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+            if (is_readable($local_file))\n+                continue;\n+\n+            if ($s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n+                $objeto = $s3->getObject(array(\n+                    'Bucket' => AWS_WSFE,\n+                    'Key'    => $s3_file));\n+\n+                file_put_contents($local_file, $objeto['Body']);\n+                continue;\n+            }\n+\n+            mostrar_error(\"Se solicitó descargar_wsfe pero la extension $extension no existe para el cuit $cuit\", true);\n+        }\n+    }\n+}\n+\n+function subir_wsfe($cuit)\n+{\n+    $s3 = conectar_aws('s3');\n+    $extensiones = array('key', 'req', 'ini');\n+\n+    foreach ($extensiones as $extension) {\n+        $s3->putObject(array(\n+            'Bucket' => AWS_WSFE,\n+            'Key' => $cuit .'/'.$cuit.'.'.$extension,\n+            'Body' => Guzzle\\Http\\EntityBody::factory(fopen(PATH_WSFE.$cuit.'.'.$extension, 'r')),\n+        ));\n+    }\n+}\n+\n+function baja_wsfe($cuit, $extension)\n+{\n+    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+\n+    $s3 = conectar_aws('s3');\n+    if (!$s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n+        unlink(PATH_WSFE.$cuit.'.'.$extension);\n+        return true;\n+    }\n+\n+    if ($s3->deleteObjects(array(\n+        'Bucket' => AWS_WSFE,\n+        'Objects' => array(array('Key' => $s3_file))))) {\n+       unlink(PATH_WSFE.$cuit.'.'.$extension);\n+       return true;\n+    }\n+}\n+\n+function crear_png($temp_imagen)\n+{\n+    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127); // 127 = transparente\n+    //imagecolortransparent($temp_imagen, $colorTransparente);\n+    imagefill($temp_imagen, 0, 0, $colorTransparente);\n+    imagesavealpha($temp_imagen, true);\n+\n+    return $temp_imagen;\n+}\n+\n+function email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])\n+{\n+    // SQS Message must be shorter than 262144 bytes\n+    if ((strlen($texto)\n+        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) { // 256 KB\n+            mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);\n+        return false;\n+    }\n+\n+    try {\n+        $sqs = conectar_aws('sqs');\n+\n+        if (is_array($destinatario)) {\n+            $copia = $destinatario[1];\n+            $destinatario = $destinatario[0];\n+        } else {\n+            $copia = false;\n+        }\n+\n+        if (is_array($remitente)) {\n+            $remitente_nombre = current($remitente);\n+            $remitente_mail = key($remitente);\n+        } else {\n+            $remitente_mail = $remitente_nombre = $remitente;\n+        }\n+\n+        if ($remitente_mail == '<EMAIL>') {\n+            mostrar_error('Intento de <NAME_EMAIL> con el texto:<br>'.$texto, true);\n+            return true;\n+        }\n+\n+        $messageAttributes = [\n+            \"Subject\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $asunto\n+            ],\n+            \"To\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $destinatario\n+            ],\n+            \"From\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_mail\n+            ],\n+            \"FromName\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_nombre\n+            ],\n+        ];\n+\n+        if ($copia)\n+            $messageAttributes['Cc'] = [\n+                'DataType' => \"String\",\n+                'StringValue' => $copia\n+            ];\n+        if ($copia_remitente)\n+            $messageAttributes['Bcc'] = [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_mail\n+            ];\n+\n+        if (count($adjuntos)) {\n+            $messageAttributes['FileName'] = [\n+                'DataType' => 'String',\n+                'StringValue' => $adjuntos[0]['nombre'],\n+            ];\n+            $messageAttributes['FileContent'] = [\n+                'DataType' => 'String',\n+                'StringValue' => base64_encode($adjuntos[0]['valor']),\n+            ];\n+        }\n+\n+        $params = [\n+            'QueueUrl' => AWS_URL_EMAIL_QUEUE,\n+            'DelaySeconds' => 10,\n+            'MessageBody' => $texto.'<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>',\n+            'MessageAttributes' => $messageAttributes,\n+        ];\n+\n+        $sqs->sendMessage($params);\n+\n+    } catch (Exception $e) {\n+        mostrar_error('No se envió un mail con SQS.<br>'\n+            .'Error: '.$e->getMessage().'<br>'\n+            .'Parametros: '.json_encode($params), true);\n+        return false;\n+    }\n+    return true;\n+}\n+\n+function afipsdk_lambda($idempresa, $idventa)\n+{\n+    try {\n+        $sqs = conectar_aws('sqs');\n+\n+        $params = [\n+            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n+            'DelaySeconds' => 10,\n+            'MessageBody' => \"$idempresa|$idventa\",\n+        ];\n+\n+        $sqs->sendMessage($params);\n+\n+    } catch (Exception $e) {\n+        mostrar_error('No se envió un mensaje con SQS.<br>'\n+            .'Error: '.$e->getMessage().'<br>'\n+            .'Parametros: '.json_encode($params), true);\n+        return false;\n+    }\n+    return true;\n+}\n"}, {"date": 1750186003787, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -274,263 +274,7 @@\n             .'Error: '.$e->getMessage().'<br>'\n             .'Parametros: '.json_encode($params), true);\n         return false;\n     }\n+    echo 'mande a aprobar';\n     return true;\n }\n-<?php\n-\n-use Aws\\Common\\Aws;\n-use Guzzle\\Http\\EntityBody;\n-\n-function conectar_aws($servicio)\n-{\n-    try {\n-        switch ($servicio) {\n-            case 's3':\n-                $aws = Aws::factory([\n-                    'key' => AWS_KEY,\n-                    'secret' => AWS_SECRET,\n-                    'region' => AWS_REGION])\n-                    ->get('s3');\n-                break;\n-\n-            case 'sqs':\n-                $aws = Aws::factory([\n-                    'key' => AWS_SQS_QUEUER_KEY,\n-                    'secret' => AWS_SQS_QUEUER_SECRET,\n-                    'region' => AWS_REGION])\n-                    ->get('sqs');\n-                break;\n-        }\n-        return $aws;\n-\n-    } catch (Exception $e) {\n-        mostrar_error('Error al conectarse con AWS '.$servicio, true);\n-    }\n-}\n-\n-function archivos_baja($ids = array())\n-{\n-    global $modulo;\n-    global $id;\n-\n-    if (!AWS_KEY)\n-        return true;\n-\n-    $s3 = conectar_aws('s3');\n-\n-    if (count($ids)) {\n-        $str_ids = '';\n-        foreach ($ids as $value)\n-            $str_ids.= \"'$value',\";\n-        $str_ids[mb_strlen($str_ids) - 1] = ' ';\n-        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE idarchivo IN ($str_ids)\");\n-\n-    } else {\n-        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE modulo='\".$modulo.\"' AND id='\".$id.\"'\");\n-    }\n-\n-    while ($archivo = array_sql($resultado_sql)) {\n-\n-        if (!$_SESSION['perfil_archivos_baja_todos'] && $archivo['idusuario'] != $_SESSION['usuario_idusuario'])\n-            return false;\n-\n-        $objetos = array();\n-        if ($archivo['tipo'] == 'imagen') {\n-            $objetos[] = array('Key' => $archivo['iua'].'/imagen');\n-            $objetos[] = array('Key' => $archivo['iua'].'/miniatura');\n-        }\n-        $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);\n-        $objetos[] = array('Key' => $archivo['iua']);\n-\n-        if (!$s3->deleteObjects(array('Bucket' => AWS_BUCKET, 'Objects' => $objetos)))\n-            return false;\n-\n-    }\n-\n-    if (count($ids))\n-        consulta_sql(\"DELETE FROM archivos WHERE idarchivo IN ($str_ids)\");\n-    else\n-        consulta_sql(\"DELETE FROM archivos WHERE modulo = '$modulo' AND id = '$id'\");\n-\n-    return true;\n-}\n-\n-function existe_wsfe($cuit, $extension)\n-{\n-    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n-    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n-\n-    if (is_readable($local_file))\n-        return true;\n-\n-    if (ESTADO == 'desarrollo' && AWS_SECRET == '')\n-        return false;\n-\n-    $s3 = conectar_aws('s3');\n-    if (!$s3->doesObjectExist(AWS_WSFE, $cuit.'/'.$cuit.'.'.$extension))\n-        return false;\n-\n-    descargar_wsfe($cuit, $extension);\n-    return true;\n-}\n-\n-function descargar_wsfe($cuit, $unica_extension = false)\n-{\n-    $s3 = conectar_aws('s3');\n-    $extensiones = array('key', 'req', 'ini', 'crt');\n-\n-    foreach ($extensiones as $extension) {\n-        if (!$unica_extension || $unica_extension == $extension) {\n-\n-            $local_file = PATH_WSFE.$cuit.'.'.$extension;\n-            $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n-            if (is_readable($local_file))\n-                continue;\n-\n-            if ($s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n-                $objeto = $s3->getObject(array(\n-                    'Bucket' => AWS_WSFE,\n-                    'Key'    => $s3_file));\n-\n-                file_put_contents($local_file, $objeto['Body']);\n-                continue;\n-            }\n-\n-            mostrar_error(\"Se solicitó descargar_wsfe pero la extension $extension no existe para el cuit $cuit\", true);\n-        }\n-    }\n-}\n-\n-function subir_wsfe($cuit)\n-{\n-    $s3 = conectar_aws('s3');\n-    $extensiones = array('key', 'req', 'ini');\n-\n-    foreach ($extensiones as $extension) {\n-        $s3->putObject(array(\n-            'Bucket' => AWS_WSFE,\n-            'Key' => $cuit .'/'.$cuit.'.'.$extension,\n-            'Body' => Guzzle\\Http\\EntityBody::factory(fopen(PATH_WSFE.$cuit.'.'.$extension, 'r')),\n-        ));\n-    }\n-}\n-\n-function baja_wsfe($cuit, $extension)\n-{\n-    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n-    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n-\n-    $s3 = conectar_aws('s3');\n-    if (!$s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n-        unlink(PATH_WSFE.$cuit.'.'.$extension);\n-        return true;\n-    }\n-\n-    if ($s3->deleteObjects(array(\n-        'Bucket' => AWS_WSFE,\n-        'Objects' => array(array('Key' => $s3_file))))) {\n-       unlink(PATH_WSFE.$cuit.'.'.$extension);\n-       return true;\n-    }\n-}\n-\n-function crear_png($temp_imagen)\n-{\n-    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127); // 127 = transparente\n-    //imagecolortransparent($temp_imagen, $colorTransparente);\n-    imagefill($temp_imagen, 0, 0, $colorTransparente);\n-    imagesavealpha($temp_imagen, true);\n-\n-    return $temp_imagen;\n-}\n-\n-function email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])\n-{\n-    // SQS Message must be shorter than 262144 bytes\n-    if ((strlen($texto)\n-        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) { // 256 KB\n-            mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);\n-        return false;\n-    }\n-\n-    try {\n-        $sqs = conectar_aws('sqs');\n-\n-        if (is_array($destinatario)) {\n-            $copia = $destinatario[1];\n-            $destinatario = $destinatario[0];\n-        } else {\n-            $copia = false;\n-        }\n-\n-        if (is_array($remitente)) {\n-            $remitente_nombre = current($remitente);\n-            $remitente_mail = key($remitente);\n-        } else {\n-            $remitente_mail = $remitente_nombre = $remitente;\n-        }\n-\n-        if ($remitente_mail == '<EMAIL>') {\n-            mostrar_error('Intento de <NAME_EMAIL> con el texto:<br>'.$texto, true);\n-            return true;\n-        }\n-\n-        $messageAttributes = [\n-            \"Subject\" => [\n-                'DataType' => \"String\",\n-                'StringValue' => $asunto\n-            ],\n-            \"To\" => [\n-                'DataType' => \"String\",\n-                'StringValue' => $destinatario\n-            ],\n-            \"From\" => [\n-                'DataType' => \"String\",\n-                'StringValue' => $remitente_mail\n-            ],\n-            \"FromName\" => [\n-                'DataType' => \"String\",\n-                'StringValue' => $remitente_nombre\n-            ],\n-        ];\n-\n-        if ($copia)\n-            $messageAttributes['Cc'] = [\n-                'DataType' => \"String\",\n-                'StringValue' => $copia\n-            ];\n-        if ($copia_remitente)\n-            $messageAttributes['Bcc'] = [\n-                'DataType' => \"String\",\n-                'StringValue' => $remitente_mail\n-            ];\n-\n-        if (count($adjuntos)) {\n-            $messageAttributes['FileName'] = [\n-                'DataType' => 'String',\n-                'StringValue' => $adjuntos[0]['nombre'],\n-            ];\n-            $messageAttributes['FileContent'] = [\n-                'DataType' => 'String',\n-                'StringValue' => base64_encode($adjuntos[0]['valor']),\n-            ];\n-        }\n-\n-        $params = [\n-            'QueueUrl' => AWS_URL_EMAIL_QUEUE,\n-            'DelaySeconds' => 10,\n-            'MessageBody' => $texto.'<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>',\n-            'MessageAttributes' => $messageAttributes,\n-        ];\n-\n-        $sqs->sendMessage($params);\n-\n-    } catch (Exception $e) {\n-        mostrar_error('No se envió un mail con SQS.<br>'\n-            .'Error: '.$e->getMessage().'<br>'\n-            .'Parametros: '.json_encode($params), true);\n-        return false;\n-    }\n-    return true;\n-}\n"}, {"date": 1750186904969, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -274,7 +274,6 @@\n             .'Error: '.$e->getMessage().'<br>'\n             .'Parametros: '.json_encode($params), true);\n         return false;\n     }\n-    echo 'mande a aprobar';\n     return true;\n }\n"}, {"date": 1750445359123, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -260,10 +260,13 @@\n {\n     try {\n         $sqs = conectar_aws('sqs');\n \n+        // Determinar la cola según el ambiente de la empresa\n+        $queueUrl = obtener_cola_afipsdk($idempresa);\n+\n         $params = [\n-            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n+            'QueueUrl' => $queueUrl,\n             'DelaySeconds' => 10,\n             'MessageBody' => \"$idempresa|$idventa\",\n         ];\n \n@@ -276,4 +279,32 @@\n         return false;\n     }\n     return true;\n }\n+\n+/**\n+ * Obtiene la URL de la cola SQS según el ambiente de la empresa\n+ */\n+function obtener_cola_afipsdk($idempresa)\n+{\n+    // Determinar el ambiente basado en la versión del servidor de la sesión\n+    $ambiente = 'prod'; // Por defecto producción\n+    \n+    if (isset($_SESSION['servidor_version'])) {\n+        if ($_SESSION['servidor_version'] == 'BETA') {\n+            $ambiente = 'beta';\n+        } elseif ($_SESSION['servidor_version'] == 'ALFA') {\n+            $ambiente = 'alfa';\n+        }\n+    }\n+    \n+    // Determinar la cola según el ambiente\n+    switch ($ambiente) {\n+        case 'alfa':\n+            return AWS_URL_AFIPSDK_QUEUE_ALFA;\n+        case 'beta':\n+            return AWS_URL_AFIPSDK_QUEUE_BETA;\n+        case 'prod':\n+        default:\n+            return AWS_URL_AFIPSDK_QUEUE_PROD;\n+    }\n+}\n"}, {"date": 1750459286343, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -260,13 +260,10 @@\n {\n     try {\n         $sqs = conectar_aws('sqs');\n \n-        // Determinar la cola según el ambiente de la empresa\n-        $queueUrl = obtener_cola_afipsdk($idempresa);\n-\n         $params = [\n-            'QueueUrl' => $queueUrl,\n+            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE.'-'.strtolower($_SESSION['servidor_version']),\n             'DelaySeconds' => 10,\n             'MessageBody' => \"$idempresa|$idventa\",\n         ];\n \n@@ -279,32 +276,4 @@\n         return false;\n     }\n     return true;\n }\n-\n-/**\n- * Obtiene la URL de la cola SQS según el ambiente de la empresa\n- */\n-function obtener_cola_afipsdk($idempresa)\n-{\n-    // Determinar el ambiente basado en la versión del servidor de la sesión\n-    $ambiente = 'prod'; // Por defecto producción\n-    \n-    if (isset($_SESSION['servidor_version'])) {\n-        if ($_SESSION['servidor_version'] == 'BETA') {\n-            $ambiente = 'beta';\n-        } elseif ($_SESSION['servidor_version'] == 'ALFA') {\n-            $ambiente = 'alfa';\n-        }\n-    }\n-    \n-    // Determinar la cola según el ambiente\n-    switch ($ambiente) {\n-        case 'alfa':\n-            return AWS_URL_AFIPSDK_QUEUE_ALFA;\n-        case 'beta':\n-            return AWS_URL_AFIPSDK_QUEUE_BETA;\n-        case 'prod':\n-        default:\n-            return AWS_URL_AFIPSDK_QUEUE_PROD;\n-    }\n-}\n"}, {"date": 1750770100435, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -261,9 +261,9 @@\n     try {\n         $sqs = conectar_aws('sqs');\n \n         $params = [\n-            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE.'-'.strtolower($_SESSION['servidor_version']),\n+            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n             'DelaySeconds' => 10,\n             'MessageBody' => \"$idempresa|$idventa\",\n         ];\n \n"}, {"date": 1750770154304, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -261,8 +261,9 @@\n     try {\n         $sqs = conectar_aws('sqs');\n \n         $params = [\n+            // 'QueueUrl' => AWS_URL_AFIPSDK_QUEUE.'-'.strtolower($_SESSION['servidor_version']),\n             'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n             'DelaySeconds' => 10,\n             'MessageBody' => \"$idempresa|$idventa\",\n         ];\n"}, {"date": 1753816662677, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,27 +1,33 @@\n <?php\n \n-use Aws\\Common\\Aws;\n-use Guzzle\\Http\\EntityBody;\n+use Aws\\S3\\S3Client;\n+use Aws\\Sqs\\SqsClient;\n \n function conectar_aws($servicio)\n {\n     try {\n         switch ($servicio) {\n             case 's3':\n-                $aws = Aws::factory([\n-                    'key' => AWS_KEY,\n-                    'secret' => AWS_SECRET,\n-                    'region' => AWS_REGION])\n-                    ->get('s3');\n+                $aws = new S3Client([\n+                    'version' => 'latest',\n+                    'region' => AWS_REGION,\n+                    'credentials' => [\n+                        'key' => AWS_KEY,\n+                        'secret' => AWS_SECRET,\n+                    ],\n+                ]);\n                 break;\n \n             case 'sqs':\n-                $aws = Aws::factory([\n-                    'key' => AWS_SQS_QUEUER_KEY,\n-                    'secret' => AWS_SQS_QUEUER_SECRET,\n-                    'region' => AWS_REGION])\n-                    ->get('sqs');\n+                $aws = new SqsClient([\n+                    'version' => 'latest',\n+                    'region' => AWS_REGION,\n+                    'credentials' => [\n+                        'key' => AWS_SQS_QUEUER_KEY,\n+                        'secret' => AWS_SQS_QUEUER_SECRET,\n+                    ],\n+                ]);\n                 break;\n         }\n         return $aws;\n \n@@ -131,9 +137,9 @@\n     foreach ($extensiones as $extension) {\n         $s3->putObject(array(\n             'Bucket' => AWS_WSFE,\n             'Key' => $cuit .'/'.$cuit.'.'.$extension,\n-            'Body' => Guzzle\\Http\\EntityBody::factory(fopen(PATH_WSFE.$cuit.'.'.$extension, 'r')),\n+            'SourceFile' => PATH_WSFE.$cuit.'.'.$extension,\n         ));\n     }\n }\n \n@@ -157,21 +163,19 @@\n }\n \n function crear_png($temp_imagen)\n {\n-    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127); // 127 = transparente\n-    //imagecolortransparent($temp_imagen, $colorTransparente);\n+    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127);\n     imagefill($temp_imagen, 0, 0, $colorTransparente);\n     imagesavealpha($temp_imagen, true);\n \n     return $temp_imagen;\n }\n \n function email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])\n {\n-    // SQS Message must be shorter than 262144 bytes\n     if ((strlen($texto)\n-        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) { // 256 KB\n+        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) {\n             mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);\n         return false;\n     }\n \n@@ -254,27 +258,4 @@\n         return false;\n     }\n     return true;\n }\n-\n-function afipsdk_lambda($idempresa, $idventa)\n-{\n-    try {\n-        $sqs = conectar_aws('sqs');\n-\n-        $params = [\n-            // 'QueueUrl' => AWS_URL_AFIPSDK_QUEUE.'-'.strtolower($_SESSION['servidor_version']),\n-            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n-            'DelaySeconds' => 10,\n-            'MessageBody' => \"$idempresa|$idventa\",\n-        ];\n-\n-        $sqs->sendMessage($params);\n-\n-    } catch (Exception $e) {\n-        mostrar_error('No se envió un mensaje con SQS.<br>'\n-            .'Error: '.$e->getMessage().'<br>'\n-            .'Parametros: '.json_encode($params), true);\n-        return false;\n-    }\n-    return true;\n-}\n"}, {"date": 1753817538778, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -69,9 +69,14 @@\n         }\n         $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);\n         $objetos[] = array('Key' => $archivo['iua']);\n \n-        if (!$s3->deleteObjects(array('Bucket' => AWS_BUCKET, 'Objects' => $objetos)))\n+        if (!$s3->deleteObjects(array(\n+            'Bucket' => AWS_BUCKET,\n+            'Delete' => array(\n+                'Objects' => $objetos\n+            )\n+        )))\n             return false;\n \n     }\n \n"}, {"date": 1753824124682, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,295 @@\n+<?php\n+\n+use Aws\\S3\\S3Client;\n+use Aws\\Sqs\\SqsClient;\n+\n+function conectar_aws($servicio)\n+{\n+    try {\n+        switch ($servicio) {\n+            case 's3':\n+                $aws = new S3Client([\n+                    'version' => 'latest',\n+                    'region' => AWS_REGION,\n+                    'credentials' => [\n+                        'key' => AWS_KEY,\n+                        'secret' => AWS_SECRET,\n+                    ],\n+                ]);\n+                break;\n+\n+            case 'sqs':\n+                $aws = new SqsClient([\n+                    'version' => 'latest',\n+                    'region' => AWS_REGION,\n+                    'credentials' => [\n+                        'key' => AWS_SQS_QUEUER_KEY,\n+                        'secret' => AWS_SQS_QUEUER_SECRET,\n+                    ],\n+                ]);\n+                break;\n+        }\n+        return $aws;\n+\n+    } catch (Exception $e) {\n+        mostrar_error('Error al conectarse con AWS '.$servicio, true);\n+    }\n+}\n+\n+function archivos_baja($ids = array())\n+{\n+    global $modulo;\n+    global $id;\n+\n+    if (!AWS_KEY)\n+        return true;\n+\n+    $s3 = conectar_aws('s3');\n+\n+    if (count($ids)) {\n+        $str_ids = '';\n+        foreach ($ids as $value)\n+            $str_ids.= \"'$value',\";\n+        $str_ids[mb_strlen($str_ids) - 1] = ' ';\n+        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE idarchivo IN ($str_ids)\");\n+\n+    } else {\n+        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE modulo='\".$modulo.\"' AND id='\".$id.\"'\");\n+    }\n+\n+    while ($archivo = array_sql($resultado_sql)) {\n+\n+        if (!$_SESSION['perfil_archivos_baja_todos'] && $archivo['idusuario'] != $_SESSION['usuario_idusuario'])\n+            return false;\n+\n+        $objetos = array();\n+        if ($archivo['tipo'] == 'imagen') {\n+            $objetos[] = array('Key' => $archivo['iua'].'/imagen');\n+            $objetos[] = array('Key' => $archivo['iua'].'/miniatura');\n+        }\n+        $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);\n+        $objetos[] = array('Key' => $archivo['iua']);\n+\n+        if (!$s3->deleteObjects(array(\n+            'Bucket' => AWS_BUCKET,\n+            'Delete' => array(\n+                'Objects' => $objetos\n+            )\n+        )))\n+            return false;\n+\n+    }\n+\n+    if (count($ids))\n+        consulta_sql(\"DELETE FROM archivos WHERE idarchivo IN ($str_ids)\");\n+    else\n+        consulta_sql(\"DELETE FROM archivos WHERE modulo = '$modulo' AND id = '$id'\");\n+\n+    return true;\n+}\n+\n+function existe_wsfe($cuit, $extension)\n+{\n+    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+\n+    if (is_readable($local_file))\n+        return true;\n+\n+    if (ESTADO == 'desarrollo' && AWS_SECRET == '')\n+        return false;\n+\n+    $s3 = conectar_aws('s3');\n+\n+    try {\n+        $s3->headObject([\n+            'Bucket' => AWS_WSFE,\n+            'Key' => $s3_file\n+        ]);\n+        // Si no lanza excepción, el objeto existe\n+        descargar_wsfe($cuit, $extension);\n+        return true;\n+    } catch (Aws\\S3\\Exception\\S3Exception $e) {\n+        if ($e->getStatusCode() == 404) {\n+            return false;\n+        }\n+        // Re-lanzar otros errores\n+        throw $e;\n+    }\n+}\n+\n+function descargar_wsfe($cuit, $unica_extension = false)\n+{\n+    $s3 = conectar_aws('s3');\n+    $extensiones = array('key', 'req', 'ini', 'crt');\n+\n+    foreach ($extensiones as $extension) {\n+        if (!$unica_extension || $unica_extension == $extension) {\n+\n+            $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+            $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+            if (is_readable($local_file))\n+                continue;\n+\n+            if ($s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n+                $objeto = $s3->getObject(array(\n+                    'Bucket' => AWS_WSFE,\n+                    'Key'    => $s3_file));\n+\n+                file_put_contents($local_file, $objeto['Body']);\n+                continue;\n+            }\n+\n+            mostrar_error(\"Se solicitó descargar_wsfe pero la extension $extension no existe para el cuit $cuit\", true);\n+        }\n+    }\n+}\n+\n+function subir_wsfe($cuit)\n+{\n+    $s3 = conectar_aws('s3');\n+    $extensiones = array('key', 'req', 'ini');\n+\n+    foreach ($extensiones as $extension) {\n+        $s3->putObject(array(\n+            'Bucket' => AWS_WSFE,\n+            'Key' => $cuit .'/'.$cuit.'.'.$extension,\n+            'SourceFile' => PATH_WSFE.$cuit.'.'.$extension,\n+        ));\n+    }\n+}\n+\n+function baja_wsfe($cuit, $extension)\n+{\n+    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n+    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n+\n+    $s3 = conectar_aws('s3');\n+\n+    try {\n+        $s3->headObject([\n+            'Bucket' => AWS_WSFE,\n+            'Key' => $s3_file\n+        ]);\n+        // El objeto existe, proceder a eliminarlo\n+    } catch (Aws\\S3\\Exception\\S3Exception $e) {\n+        if ($e->getStatusCode() == 404) {\n+            // El objeto no existe en S3, solo eliminar archivo local\n+            if (file_exists($local_file)) {\n+                unlink($local_file);\n+            }\n+            return true;\n+        }\n+        throw $e;\n+    }\n+\n+    if ($s3->deleteObjects([\n+        'Bucket' => AWS_WSFE,\n+        'Delete' => [\n+            'Objects' => [['Key' => $s3_file]]\n+        ]\n+    ])) {\n+        if (file_exists($local_file)) {\n+            unlink($local_file);\n+        }\n+        return true;\n+    }\n+}\n+\n+function crear_png($temp_imagen)\n+{\n+    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127);\n+    imagefill($temp_imagen, 0, 0, $colorTransparente);\n+    imagesavealpha($temp_imagen, true);\n+\n+    return $temp_imagen;\n+}\n+\n+function email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])\n+{\n+    if ((strlen($texto)\n+        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) {\n+            mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);\n+        return false;\n+    }\n+\n+    try {\n+        $sqs = conectar_aws('sqs');\n+\n+        if (is_array($destinatario)) {\n+            $copia = $destinatario[1];\n+            $destinatario = $destinatario[0];\n+        } else {\n+            $copia = false;\n+        }\n+\n+        if (is_array($remitente)) {\n+            $remitente_nombre = current($remitente);\n+            $remitente_mail = key($remitente);\n+        } else {\n+            $remitente_mail = $remitente_nombre = $remitente;\n+        }\n+\n+        if ($remitente_mail == '<EMAIL>') {\n+            mostrar_error('Intento de <NAME_EMAIL> con el texto:<br>'.$texto, true);\n+            return true;\n+        }\n+\n+        $messageAttributes = [\n+            \"Subject\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $asunto\n+            ],\n+            \"To\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $destinatario\n+            ],\n+            \"From\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_mail\n+            ],\n+            \"FromName\" => [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_nombre\n+            ],\n+        ];\n+\n+        if ($copia)\n+            $messageAttributes['Cc'] = [\n+                'DataType' => \"String\",\n+                'StringValue' => $copia\n+            ];\n+        if ($copia_remitente)\n+            $messageAttributes['Bcc'] = [\n+                'DataType' => \"String\",\n+                'StringValue' => $remitente_mail\n+            ];\n+\n+        if (count($adjuntos)) {\n+            $messageAttributes['FileName'] = [\n+                'DataType' => 'String',\n+                'StringValue' => $adjuntos[0]['nombre'],\n+            ];\n+            $messageAttributes['FileContent'] = [\n+                'DataType' => 'String',\n+                'StringValue' => base64_encode($adjuntos[0]['valor']),\n+            ];\n+        }\n+\n+        $params = [\n+            'QueueUrl' => AWS_URL_EMAIL_QUEUE,\n+            'DelaySeconds' => 10,\n+            'MessageBody' => $texto.'<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>',\n+            'MessageAttributes' => $messageAttributes,\n+        ];\n+\n+        $sqs->sendMessage($params);\n+\n+    } catch (Exception $e) {\n+        mostrar_error('No se envió un mail con SQS.<br>'\n+            .'Error: '.$e->getMessage().'<br>'\n+            .'Parametros: '.json_encode($params), true);\n+        return false;\n+    }\n+    return true;\n+}\n"}], "date": 1732072312629, "name": "Commit-0", "content": "<?php\n\nuse Aws\\Common\\Aws;\nuse Guzzle\\Http\\EntityBody;\n\nfunction conectar_aws($servicio)\n{\n    try {\n        switch ($servicio) {\n            case 's3':\n                $aws = Aws::factory([\n                    'key' => AWS_KEY,\n                    'secret' => AWS_SECRET,\n                    'region' => AWS_REGION])\n                    ->get('s3');\n                break;\n\n            case 'sqs':\n                $aws = Aws::factory([\n                    'key' => AWS_SQS_QUEUER_KEY,\n                    'secret' => AWS_SQS_QUEUER_SECRET,\n                    'region' => AWS_REGION])\n                    ->get('sqs');\n                break;\n        }\n        return $aws;\n\n    } catch (Exception $e) {\n        mostrar_error('Error al conectarse con AWS '.$servicio, true);\n    }\n}\n\nfunction archivos_baja($ids = array())\n{\n    global $modulo;\n    global $id;\n\n    if (!AWS_KEY)\n        return true;\n\n    $s3 = conectar_aws('s3');\n\n    if (count($ids)) {\n        $str_ids = '';\n        foreach ($ids as $value)\n            $str_ids.= \"'$value',\";\n        $str_ids[mb_strlen($str_ids) - 1] = ' ';\n        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE idarchivo IN ($str_ids)\");\n\n    } else {\n        $resultado_sql = consulta_sql(\"SELECT iua, archivo, url, tipo, idusuario FROM archivos WHERE modulo='\".$modulo.\"' AND id='\".$id.\"'\");\n    }\n\n    while ($archivo = array_sql($resultado_sql)) {\n\n        if (!$_SESSION['perfil_archivos_baja_todos'] && $archivo['idusuario'] != $_SESSION['usuario_idusuario'])\n            return false;\n\n        $objetos = array();\n        if ($archivo['tipo'] == 'imagen') {\n            $objetos[] = array('Key' => $archivo['iua'].'/imagen');\n            $objetos[] = array('Key' => $archivo['iua'].'/miniatura');\n        }\n        $objetos[] = array('Key' => $archivo['iua'].'/'.$archivo['archivo']);\n        $objetos[] = array('Key' => $archivo['iua']);\n\n        if (!$s3->deleteObjects(array('Bucket' => AWS_BUCKET, 'Objects' => $objetos)))\n            return false;\n\n    }\n\n    if (count($ids))\n        consulta_sql(\"DELETE FROM archivos WHERE idarchivo IN ($str_ids)\");\n    else\n        consulta_sql(\"DELETE FROM archivos WHERE modulo = '$modulo' AND id = '$id'\");\n\n    return true;\n}\n\nfunction existe_wsfe($cuit, $extension)\n{\n    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n\n    if (is_readable($local_file))\n        return true;\n\n    if (ESTADO == 'desarrollo' && AWS_SECRET == '')\n        return false;\n\n    $s3 = conectar_aws('s3');\n    if (!$s3->doesObjectExist(AWS_WSFE, $cuit.'/'.$cuit.'.'.$extension))\n        return false;\n\n    descargar_wsfe($cuit, $extension);\n    return true;\n}\n\nfunction descargar_wsfe($cuit, $unica_extension = false)\n{\n    $s3 = conectar_aws('s3');\n    $extensiones = array('key', 'req', 'ini', 'crt');\n\n    foreach ($extensiones as $extension) {\n        if (!$unica_extension || $unica_extension == $extension) {\n\n            $local_file = PATH_WSFE.$cuit.'.'.$extension;\n            $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n            if (is_readable($local_file))\n                continue;\n\n            if ($s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n                $objeto = $s3->getObject(array(\n                    'Bucket' => AWS_WSFE,\n                    'Key'    => $s3_file));\n\n                file_put_contents($local_file, $objeto['Body']);\n                continue;\n            }\n\n            mostrar_error(\"Se solicitó descargar_wsfe pero la extension $extension no existe para el cuit $cuit\", true);\n        }\n    }\n}\n\nfunction subir_wsfe($cuit)\n{\n    $s3 = conectar_aws('s3');\n    $extensiones = array('key', 'req', 'ini');\n\n    foreach ($extensiones as $extension) {\n        $s3->putObject(array(\n            'Bucket' => AWS_WSFE,\n            'Key' => $cuit .'/'.$cuit.'.'.$extension,\n            'Body' => Guzzle\\Http\\EntityBody::factory(fopen(PATH_WSFE.$cuit.'.'.$extension, 'r')),\n        ));\n    }\n}\n\nfunction baja_wsfe($cuit, $extension)\n{\n    $local_file = PATH_WSFE.$cuit.'.'.$extension;\n    $s3_file = $cuit.'/'.$cuit.'.'.$extension;\n\n    $s3 = conectar_aws('s3');\n    if (!$s3->doesObjectExist(AWS_WSFE, $s3_file)) {\n        unlink(PATH_WSFE.$cuit.'.'.$extension);\n        return true;\n    }\n\n    if ($s3->deleteObjects(array(\n        'Bucket' => AWS_WSFE,\n        'Objects' => array(array('Key' => $s3_file))))) {\n       unlink(PATH_WSFE.$cuit.'.'.$extension);\n       return true;\n    }\n}\n\nfunction crear_png($temp_imagen)\n{\n    $colorTransparente  = imagecolorallocatealpha($temp_imagen, 0, 0, 0, 127); // 127 = transparente\n    //imagecolortransparent($temp_imagen, $colorTransparente);\n    imagefill($temp_imagen, 0, 0, $colorTransparente);\n    imagesavealpha($temp_imagen, true);\n\n    return $temp_imagen;\n}\n\nfunction email_queue($remitente, $destinatario, $asunto, $texto, $copia_remitente = false, $adjuntos = [])\n{\n    // SQS Message must be shorter than 262144 bytes\n    if ((strlen($texto)\n        + (count($adjuntos) && isset($adjuntos[0]['valor']) ? strlen($adjuntos[0]['valor']) : 0)) > 260000) { // 256 KB\n            mostrar_error('No se envió un mail con SQS porque el texto es demasiado largo: '.(strlen($texto) + strlen($adjuntos[0]['valor'])).' bytes', true);\n        return false;\n    }\n\n    try {\n        $sqs = conectar_aws('sqs');\n\n        if (is_array($destinatario)) {\n            $copia = $destinatario[1];\n            $destinatario = $destinatario[0];\n        } else {\n            $copia = false;\n        }\n\n        if (is_array($remitente)) {\n            $remitente_nombre = current($remitente);\n            $remitente_mail = key($remitente);\n        } else {\n            $remitente_mail = $remitente_nombre = $remitente;\n        }\n\n        $messageAttributes = [\n            \"Subject\" => [\n                'DataType' => \"String\",\n                'StringValue' => $asunto\n            ],\n            \"To\" => [\n                'DataType' => \"String\",\n                'StringValue' => $destinatario\n            ],\n            \"From\" => [\n                'DataType' => \"String\",\n                'StringValue' => $remitente_mail\n            ],\n            \"FromName\" => [\n                'DataType' => \"String\",\n                'StringValue' => $remitente_nombre\n            ],\n        ];\n\n        if ($copia)\n            $messageAttributes['Cc'] = [\n                'DataType' => \"String\",\n                'StringValue' => $copia\n            ];\n        if ($copia_remitente)\n            $messageAttributes['Bcc'] = [\n                'DataType' => \"String\",\n                'StringValue' => $remitente_mail\n            ];\n\n        if (count($adjuntos)) {\n            $messageAttributes['FileName'] = [\n                'DataType' => 'String',\n                'StringValue' => $adjuntos[0]['nombre'],\n            ];\n            $messageAttributes['FileContent'] = [\n                'DataType' => 'String',\n                'StringValue' => base64_encode($adjuntos[0]['valor']),\n            ];\n        }\n\n        $params = [\n            'QueueUrl' => AWS_URL_EMAIL_QUEUE,\n            'DelaySeconds' => 10,\n            'MessageBody' => $texto.'<br><br><hr>Enviado desde <a href=\"'.URL_SITE.'\">'.URL_HOST.'</a>',\n            'MessageAttributes' => $messageAttributes,\n        ];\n\n        $sqs->sendMessage($params);\n\n    } catch (Exception $e) {\n        mostrar_error('No se envió un mail con SQS.<br>'\n            .'Error: '.$e->getMessage().'<br>'\n            .'Parametros: '.json_encode($params), true);\n        return false;\n    }\n    return true;\n}\n"}]}