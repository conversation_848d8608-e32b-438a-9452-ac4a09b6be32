<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'AWS\\CRT\\Auth\\AwsCredentials' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/AwsCredentials.php',
    'AWS\\CRT\\Auth\\CredentialsProvider' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/CredentialsProvider.php',
    'AWS\\CRT\\Auth\\Signable' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/Signable.php',
    'AWS\\CRT\\Auth\\SignatureType' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/SignatureType.php',
    'AWS\\CRT\\Auth\\SignedBodyHeaderType' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/SignedBodyHeaderType.php',
    'AWS\\CRT\\Auth\\Signing' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/Signing.php',
    'AWS\\CRT\\Auth\\SigningAlgorithm' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/SigningAlgorithm.php',
    'AWS\\CRT\\Auth\\SigningConfigAWS' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/SigningConfigAWS.php',
    'AWS\\CRT\\Auth\\SigningResult' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/SigningResult.php',
    'AWS\\CRT\\Auth\\StaticCredentialsProvider' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Auth/StaticCredentialsProvider.php',
    'AWS\\CRT\\CRT' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/CRT.php',
    'AWS\\CRT\\HTTP\\Headers' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/HTTP/Headers.php',
    'AWS\\CRT\\HTTP\\Message' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/HTTP/Message.php',
    'AWS\\CRT\\HTTP\\Request' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/HTTP/Request.php',
    'AWS\\CRT\\HTTP\\Response' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/HTTP/Response.php',
    'AWS\\CRT\\IO\\EventLoopGroup' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/IO/EventLoopGroup.php',
    'AWS\\CRT\\IO\\InputStream' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/IO/InputStream.php',
    'AWS\\CRT\\Internal\\Encoding' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Internal/Encoding.php',
    'AWS\\CRT\\Internal\\Extension' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Internal/Extension.php',
    'AWS\\CRT\\Log' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Log.php',
    'AWS\\CRT\\NativeResource' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/NativeResource.php',
    'AWS\\CRT\\OptionValue' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Options.php',
    'AWS\\CRT\\Options' => $vendorDir . '/aws/aws-crt-php/src/AWS/CRT/Options.php',
    'Aws\\ACMPCA\\ACMPCAClient' => $vendorDir . '/aws/aws-sdk-php/src/ACMPCA/ACMPCAClient.php',
    'Aws\\ACMPCA\\Exception\\ACMPCAException' => $vendorDir . '/aws/aws-sdk-php/src/ACMPCA/Exception/ACMPCAException.php',
    'Aws\\AIOps\\AIOpsClient' => $vendorDir . '/aws/aws-sdk-php/src/AIOps/AIOpsClient.php',
    'Aws\\AIOps\\Exception\\AIOpsException' => $vendorDir . '/aws/aws-sdk-php/src/AIOps/Exception/AIOpsException.php',
    'Aws\\ARCZonalShift\\ARCZonalShiftClient' => $vendorDir . '/aws/aws-sdk-php/src/ARCZonalShift/ARCZonalShiftClient.php',
    'Aws\\ARCZonalShift\\Exception\\ARCZonalShiftException' => $vendorDir . '/aws/aws-sdk-php/src/ARCZonalShift/Exception/ARCZonalShiftException.php',
    'Aws\\AbstractConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/AbstractConfigurationProvider.php',
    'Aws\\AccessAnalyzer\\AccessAnalyzerClient' => $vendorDir . '/aws/aws-sdk-php/src/AccessAnalyzer/AccessAnalyzerClient.php',
    'Aws\\AccessAnalyzer\\Exception\\AccessAnalyzerException' => $vendorDir . '/aws/aws-sdk-php/src/AccessAnalyzer/Exception/AccessAnalyzerException.php',
    'Aws\\Account\\AccountClient' => $vendorDir . '/aws/aws-sdk-php/src/Account/AccountClient.php',
    'Aws\\Account\\Exception\\AccountException' => $vendorDir . '/aws/aws-sdk-php/src/Account/Exception/AccountException.php',
    'Aws\\Acm\\AcmClient' => $vendorDir . '/aws/aws-sdk-php/src/Acm/AcmClient.php',
    'Aws\\Acm\\Exception\\AcmException' => $vendorDir . '/aws/aws-sdk-php/src/Acm/Exception/AcmException.php',
    'Aws\\AmplifyBackend\\AmplifyBackendClient' => $vendorDir . '/aws/aws-sdk-php/src/AmplifyBackend/AmplifyBackendClient.php',
    'Aws\\AmplifyBackend\\Exception\\AmplifyBackendException' => $vendorDir . '/aws/aws-sdk-php/src/AmplifyBackend/Exception/AmplifyBackendException.php',
    'Aws\\AmplifyUIBuilder\\AmplifyUIBuilderClient' => $vendorDir . '/aws/aws-sdk-php/src/AmplifyUIBuilder/AmplifyUIBuilderClient.php',
    'Aws\\AmplifyUIBuilder\\Exception\\AmplifyUIBuilderException' => $vendorDir . '/aws/aws-sdk-php/src/AmplifyUIBuilder/Exception/AmplifyUIBuilderException.php',
    'Aws\\Amplify\\AmplifyClient' => $vendorDir . '/aws/aws-sdk-php/src/Amplify/AmplifyClient.php',
    'Aws\\Amplify\\Exception\\AmplifyException' => $vendorDir . '/aws/aws-sdk-php/src/Amplify/Exception/AmplifyException.php',
    'Aws\\ApiGatewayManagementApi\\ApiGatewayManagementApiClient' => $vendorDir . '/aws/aws-sdk-php/src/ApiGatewayManagementApi/ApiGatewayManagementApiClient.php',
    'Aws\\ApiGatewayManagementApi\\Exception\\ApiGatewayManagementApiException' => $vendorDir . '/aws/aws-sdk-php/src/ApiGatewayManagementApi/Exception/ApiGatewayManagementApiException.php',
    'Aws\\ApiGatewayV2\\ApiGatewayV2Client' => $vendorDir . '/aws/aws-sdk-php/src/ApiGatewayV2/ApiGatewayV2Client.php',
    'Aws\\ApiGatewayV2\\Exception\\ApiGatewayV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/ApiGatewayV2/Exception/ApiGatewayV2Exception.php',
    'Aws\\ApiGateway\\ApiGatewayClient' => $vendorDir . '/aws/aws-sdk-php/src/ApiGateway/ApiGatewayClient.php',
    'Aws\\ApiGateway\\Exception\\ApiGatewayException' => $vendorDir . '/aws/aws-sdk-php/src/ApiGateway/Exception/ApiGatewayException.php',
    'Aws\\Api\\AbstractModel' => $vendorDir . '/aws/aws-sdk-php/src/Api/AbstractModel.php',
    'Aws\\Api\\ApiProvider' => $vendorDir . '/aws/aws-sdk-php/src/Api/ApiProvider.php',
    'Aws\\Api\\DateTimeResult' => $vendorDir . '/aws/aws-sdk-php/src/Api/DateTimeResult.php',
    'Aws\\Api\\DocModel' => $vendorDir . '/aws/aws-sdk-php/src/Api/DocModel.php',
    'Aws\\Api\\ErrorParser\\AbstractErrorParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/ErrorParser/AbstractErrorParser.php',
    'Aws\\Api\\ErrorParser\\JsonParserTrait' => $vendorDir . '/aws/aws-sdk-php/src/Api/ErrorParser/JsonParserTrait.php',
    'Aws\\Api\\ErrorParser\\JsonRpcErrorParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/ErrorParser/JsonRpcErrorParser.php',
    'Aws\\Api\\ErrorParser\\RestJsonErrorParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/ErrorParser/RestJsonErrorParser.php',
    'Aws\\Api\\ErrorParser\\XmlErrorParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/ErrorParser/XmlErrorParser.php',
    'Aws\\Api\\ListShape' => $vendorDir . '/aws/aws-sdk-php/src/Api/ListShape.php',
    'Aws\\Api\\MapShape' => $vendorDir . '/aws/aws-sdk-php/src/Api/MapShape.php',
    'Aws\\Api\\Operation' => $vendorDir . '/aws/aws-sdk-php/src/Api/Operation.php',
    'Aws\\Api\\Parser\\AbstractParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/AbstractParser.php',
    'Aws\\Api\\Parser\\AbstractRestParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/AbstractRestParser.php',
    'Aws\\Api\\Parser\\Crc32ValidatingParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/Crc32ValidatingParser.php',
    'Aws\\Api\\Parser\\DecodingEventStreamIterator' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/DecodingEventStreamIterator.php',
    'Aws\\Api\\Parser\\EventParsingIterator' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/EventParsingIterator.php',
    'Aws\\Api\\Parser\\Exception\\ParserException' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/Exception/ParserException.php',
    'Aws\\Api\\Parser\\JsonParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/JsonParser.php',
    'Aws\\Api\\Parser\\JsonRpcParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/JsonRpcParser.php',
    'Aws\\Api\\Parser\\MetadataParserTrait' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/MetadataParserTrait.php',
    'Aws\\Api\\Parser\\NonSeekableStreamDecodingEventStreamIterator' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/NonSeekableStreamDecodingEventStreamIterator.php',
    'Aws\\Api\\Parser\\PayloadParserTrait' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/PayloadParserTrait.php',
    'Aws\\Api\\Parser\\QueryParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/QueryParser.php',
    'Aws\\Api\\Parser\\RestJsonParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/RestJsonParser.php',
    'Aws\\Api\\Parser\\RestXmlParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/RestXmlParser.php',
    'Aws\\Api\\Parser\\XmlParser' => $vendorDir . '/aws/aws-sdk-php/src/Api/Parser/XmlParser.php',
    'Aws\\Api\\Serializer\\Ec2ParamBuilder' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/Ec2ParamBuilder.php',
    'Aws\\Api\\Serializer\\JsonBody' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/JsonBody.php',
    'Aws\\Api\\Serializer\\JsonRpcSerializer' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/JsonRpcSerializer.php',
    'Aws\\Api\\Serializer\\QueryParamBuilder' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/QueryParamBuilder.php',
    'Aws\\Api\\Serializer\\QuerySerializer' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/QuerySerializer.php',
    'Aws\\Api\\Serializer\\RestJsonSerializer' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/RestJsonSerializer.php',
    'Aws\\Api\\Serializer\\RestSerializer' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/RestSerializer.php',
    'Aws\\Api\\Serializer\\RestXmlSerializer' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/RestXmlSerializer.php',
    'Aws\\Api\\Serializer\\XmlBody' => $vendorDir . '/aws/aws-sdk-php/src/Api/Serializer/XmlBody.php',
    'Aws\\Api\\Service' => $vendorDir . '/aws/aws-sdk-php/src/Api/Service.php',
    'Aws\\Api\\Shape' => $vendorDir . '/aws/aws-sdk-php/src/Api/Shape.php',
    'Aws\\Api\\ShapeMap' => $vendorDir . '/aws/aws-sdk-php/src/Api/ShapeMap.php',
    'Aws\\Api\\StructureShape' => $vendorDir . '/aws/aws-sdk-php/src/Api/StructureShape.php',
    'Aws\\Api\\SupportedProtocols' => $vendorDir . '/aws/aws-sdk-php/src/Api/SupportedProtocols.php',
    'Aws\\Api\\TimestampShape' => $vendorDir . '/aws/aws-sdk-php/src/Api/TimestampShape.php',
    'Aws\\Api\\Validator' => $vendorDir . '/aws/aws-sdk-php/src/Api/Validator.php',
    'Aws\\AppConfigData\\AppConfigDataClient' => $vendorDir . '/aws/aws-sdk-php/src/AppConfigData/AppConfigDataClient.php',
    'Aws\\AppConfigData\\Exception\\AppConfigDataException' => $vendorDir . '/aws/aws-sdk-php/src/AppConfigData/Exception/AppConfigDataException.php',
    'Aws\\AppConfig\\AppConfigClient' => $vendorDir . '/aws/aws-sdk-php/src/AppConfig/AppConfigClient.php',
    'Aws\\AppConfig\\Exception\\AppConfigException' => $vendorDir . '/aws/aws-sdk-php/src/AppConfig/Exception/AppConfigException.php',
    'Aws\\AppFabric\\AppFabricClient' => $vendorDir . '/aws/aws-sdk-php/src/AppFabric/AppFabricClient.php',
    'Aws\\AppFabric\\Exception\\AppFabricException' => $vendorDir . '/aws/aws-sdk-php/src/AppFabric/Exception/AppFabricException.php',
    'Aws\\AppIntegrationsService\\AppIntegrationsServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/AppIntegrationsService/AppIntegrationsServiceClient.php',
    'Aws\\AppIntegrationsService\\Exception\\AppIntegrationsServiceException' => $vendorDir . '/aws/aws-sdk-php/src/AppIntegrationsService/Exception/AppIntegrationsServiceException.php',
    'Aws\\AppMesh\\AppMeshClient' => $vendorDir . '/aws/aws-sdk-php/src/AppMesh/AppMeshClient.php',
    'Aws\\AppMesh\\Exception\\AppMeshException' => $vendorDir . '/aws/aws-sdk-php/src/AppMesh/Exception/AppMeshException.php',
    'Aws\\AppRegistry\\AppRegistryClient' => $vendorDir . '/aws/aws-sdk-php/src/AppRegistry/AppRegistryClient.php',
    'Aws\\AppRegistry\\Exception\\AppRegistryException' => $vendorDir . '/aws/aws-sdk-php/src/AppRegistry/Exception/AppRegistryException.php',
    'Aws\\AppRunner\\AppRunnerClient' => $vendorDir . '/aws/aws-sdk-php/src/AppRunner/AppRunnerClient.php',
    'Aws\\AppRunner\\Exception\\AppRunnerException' => $vendorDir . '/aws/aws-sdk-php/src/AppRunner/Exception/AppRunnerException.php',
    'Aws\\AppSync\\AppSyncClient' => $vendorDir . '/aws/aws-sdk-php/src/AppSync/AppSyncClient.php',
    'Aws\\AppSync\\Exception\\AppSyncException' => $vendorDir . '/aws/aws-sdk-php/src/AppSync/Exception/AppSyncException.php',
    'Aws\\AppTest\\AppTestClient' => $vendorDir . '/aws/aws-sdk-php/src/AppTest/AppTestClient.php',
    'Aws\\AppTest\\Exception\\AppTestException' => $vendorDir . '/aws/aws-sdk-php/src/AppTest/Exception/AppTestException.php',
    'Aws\\Appflow\\AppflowClient' => $vendorDir . '/aws/aws-sdk-php/src/Appflow/AppflowClient.php',
    'Aws\\Appflow\\Exception\\AppflowException' => $vendorDir . '/aws/aws-sdk-php/src/Appflow/Exception/AppflowException.php',
    'Aws\\ApplicationAutoScaling\\ApplicationAutoScalingClient' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationAutoScaling/ApplicationAutoScalingClient.php',
    'Aws\\ApplicationAutoScaling\\Exception\\ApplicationAutoScalingException' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationAutoScaling/Exception/ApplicationAutoScalingException.php',
    'Aws\\ApplicationCostProfiler\\ApplicationCostProfilerClient' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationCostProfiler/ApplicationCostProfilerClient.php',
    'Aws\\ApplicationCostProfiler\\Exception\\ApplicationCostProfilerException' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationCostProfiler/Exception/ApplicationCostProfilerException.php',
    'Aws\\ApplicationDiscoveryService\\ApplicationDiscoveryServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationDiscoveryService/ApplicationDiscoveryServiceClient.php',
    'Aws\\ApplicationDiscoveryService\\Exception\\ApplicationDiscoveryServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationDiscoveryService/Exception/ApplicationDiscoveryServiceException.php',
    'Aws\\ApplicationInsights\\ApplicationInsightsClient' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationInsights/ApplicationInsightsClient.php',
    'Aws\\ApplicationInsights\\Exception\\ApplicationInsightsException' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationInsights/Exception/ApplicationInsightsException.php',
    'Aws\\ApplicationSignals\\ApplicationSignalsClient' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationSignals/ApplicationSignalsClient.php',
    'Aws\\ApplicationSignals\\Exception\\ApplicationSignalsException' => $vendorDir . '/aws/aws-sdk-php/src/ApplicationSignals/Exception/ApplicationSignalsException.php',
    'Aws\\Appstream\\AppstreamClient' => $vendorDir . '/aws/aws-sdk-php/src/Appstream/AppstreamClient.php',
    'Aws\\Appstream\\Exception\\AppstreamException' => $vendorDir . '/aws/aws-sdk-php/src/Appstream/Exception/AppstreamException.php',
    'Aws\\Arn\\AccessPointArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/AccessPointArn.php',
    'Aws\\Arn\\AccessPointArnInterface' => $vendorDir . '/aws/aws-sdk-php/src/Arn/AccessPointArnInterface.php',
    'Aws\\Arn\\Arn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/Arn.php',
    'Aws\\Arn\\ArnInterface' => $vendorDir . '/aws/aws-sdk-php/src/Arn/ArnInterface.php',
    'Aws\\Arn\\ArnParser' => $vendorDir . '/aws/aws-sdk-php/src/Arn/ArnParser.php',
    'Aws\\Arn\\Exception\\InvalidArnException' => $vendorDir . '/aws/aws-sdk-php/src/Arn/Exception/InvalidArnException.php',
    'Aws\\Arn\\ObjectLambdaAccessPointArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/ObjectLambdaAccessPointArn.php',
    'Aws\\Arn\\ResourceTypeAndIdTrait' => $vendorDir . '/aws/aws-sdk-php/src/Arn/ResourceTypeAndIdTrait.php',
    'Aws\\Arn\\S3\\AccessPointArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/AccessPointArn.php',
    'Aws\\Arn\\S3\\BucketArnInterface' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/BucketArnInterface.php',
    'Aws\\Arn\\S3\\MultiRegionAccessPointArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/MultiRegionAccessPointArn.php',
    'Aws\\Arn\\S3\\OutpostsAccessPointArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/OutpostsAccessPointArn.php',
    'Aws\\Arn\\S3\\OutpostsArnInterface' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/OutpostsArnInterface.php',
    'Aws\\Arn\\S3\\OutpostsBucketArn' => $vendorDir . '/aws/aws-sdk-php/src/Arn/S3/OutpostsBucketArn.php',
    'Aws\\Artifact\\ArtifactClient' => $vendorDir . '/aws/aws-sdk-php/src/Artifact/ArtifactClient.php',
    'Aws\\Artifact\\Exception\\ArtifactException' => $vendorDir . '/aws/aws-sdk-php/src/Artifact/Exception/ArtifactException.php',
    'Aws\\Athena\\AthenaClient' => $vendorDir . '/aws/aws-sdk-php/src/Athena/AthenaClient.php',
    'Aws\\Athena\\Exception\\AthenaException' => $vendorDir . '/aws/aws-sdk-php/src/Athena/Exception/AthenaException.php',
    'Aws\\AuditManager\\AuditManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/AuditManager/AuditManagerClient.php',
    'Aws\\AuditManager\\Exception\\AuditManagerException' => $vendorDir . '/aws/aws-sdk-php/src/AuditManager/Exception/AuditManagerException.php',
    'Aws\\AugmentedAIRuntime\\AugmentedAIRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/AugmentedAIRuntime/AugmentedAIRuntimeClient.php',
    'Aws\\AugmentedAIRuntime\\Exception\\AugmentedAIRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/AugmentedAIRuntime/Exception/AugmentedAIRuntimeException.php',
    'Aws\\Auth\\AuthSchemeResolver' => $vendorDir . '/aws/aws-sdk-php/src/Auth/AuthSchemeResolver.php',
    'Aws\\Auth\\AuthSchemeResolverInterface' => $vendorDir . '/aws/aws-sdk-php/src/Auth/AuthSchemeResolverInterface.php',
    'Aws\\Auth\\AuthSelectionMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/Auth/AuthSelectionMiddleware.php',
    'Aws\\Auth\\Exception\\UnresolvedAuthSchemeException' => $vendorDir . '/aws/aws-sdk-php/src/Auth/Exception/UnresolvedAuthSchemeException.php',
    'Aws\\AutoScalingPlans\\AutoScalingPlansClient' => $vendorDir . '/aws/aws-sdk-php/src/AutoScalingPlans/AutoScalingPlansClient.php',
    'Aws\\AutoScalingPlans\\Exception\\AutoScalingPlansException' => $vendorDir . '/aws/aws-sdk-php/src/AutoScalingPlans/Exception/AutoScalingPlansException.php',
    'Aws\\AutoScaling\\AutoScalingClient' => $vendorDir . '/aws/aws-sdk-php/src/AutoScaling/AutoScalingClient.php',
    'Aws\\AutoScaling\\Exception\\AutoScalingException' => $vendorDir . '/aws/aws-sdk-php/src/AutoScaling/Exception/AutoScalingException.php',
    'Aws\\AwsClient' => $vendorDir . '/aws/aws-sdk-php/src/AwsClient.php',
    'Aws\\AwsClientInterface' => $vendorDir . '/aws/aws-sdk-php/src/AwsClientInterface.php',
    'Aws\\AwsClientTrait' => $vendorDir . '/aws/aws-sdk-php/src/AwsClientTrait.php',
    'Aws\\B2bi\\B2biClient' => $vendorDir . '/aws/aws-sdk-php/src/B2bi/B2biClient.php',
    'Aws\\B2bi\\Exception\\B2biException' => $vendorDir . '/aws/aws-sdk-php/src/B2bi/Exception/B2biException.php',
    'Aws\\BCMDataExports\\BCMDataExportsClient' => $vendorDir . '/aws/aws-sdk-php/src/BCMDataExports/BCMDataExportsClient.php',
    'Aws\\BCMDataExports\\Exception\\BCMDataExportsException' => $vendorDir . '/aws/aws-sdk-php/src/BCMDataExports/Exception/BCMDataExportsException.php',
    'Aws\\BCMPricingCalculator\\BCMPricingCalculatorClient' => $vendorDir . '/aws/aws-sdk-php/src/BCMPricingCalculator/BCMPricingCalculatorClient.php',
    'Aws\\BCMPricingCalculator\\Exception\\BCMPricingCalculatorException' => $vendorDir . '/aws/aws-sdk-php/src/BCMPricingCalculator/Exception/BCMPricingCalculatorException.php',
    'Aws\\BackupGateway\\BackupGatewayClient' => $vendorDir . '/aws/aws-sdk-php/src/BackupGateway/BackupGatewayClient.php',
    'Aws\\BackupGateway\\Exception\\BackupGatewayException' => $vendorDir . '/aws/aws-sdk-php/src/BackupGateway/Exception/BackupGatewayException.php',
    'Aws\\BackupSearch\\BackupSearchClient' => $vendorDir . '/aws/aws-sdk-php/src/BackupSearch/BackupSearchClient.php',
    'Aws\\BackupSearch\\Exception\\BackupSearchException' => $vendorDir . '/aws/aws-sdk-php/src/BackupSearch/Exception/BackupSearchException.php',
    'Aws\\Backup\\BackupClient' => $vendorDir . '/aws/aws-sdk-php/src/Backup/BackupClient.php',
    'Aws\\Backup\\Exception\\BackupException' => $vendorDir . '/aws/aws-sdk-php/src/Backup/Exception/BackupException.php',
    'Aws\\Batch\\BatchClient' => $vendorDir . '/aws/aws-sdk-php/src/Batch/BatchClient.php',
    'Aws\\Batch\\Exception\\BatchException' => $vendorDir . '/aws/aws-sdk-php/src/Batch/Exception/BatchException.php',
    'Aws\\BedrockAgentCoreControl\\BedrockAgentCoreControlClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentCoreControl/BedrockAgentCoreControlClient.php',
    'Aws\\BedrockAgentCoreControl\\Exception\\BedrockAgentCoreControlException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentCoreControl/Exception/BedrockAgentCoreControlException.php',
    'Aws\\BedrockAgentCore\\BedrockAgentCoreClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentCore/BedrockAgentCoreClient.php',
    'Aws\\BedrockAgentCore\\Exception\\BedrockAgentCoreException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentCore/Exception/BedrockAgentCoreException.php',
    'Aws\\BedrockAgentRuntime\\BedrockAgentRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentRuntime/BedrockAgentRuntimeClient.php',
    'Aws\\BedrockAgentRuntime\\Exception\\BedrockAgentRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgentRuntime/Exception/BedrockAgentRuntimeException.php',
    'Aws\\BedrockAgent\\BedrockAgentClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgent/BedrockAgentClient.php',
    'Aws\\BedrockAgent\\Exception\\BedrockAgentException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockAgent/Exception/BedrockAgentException.php',
    'Aws\\BedrockDataAutomationRuntime\\BedrockDataAutomationRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockDataAutomationRuntime/BedrockDataAutomationRuntimeClient.php',
    'Aws\\BedrockDataAutomationRuntime\\Exception\\BedrockDataAutomationRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockDataAutomationRuntime/Exception/BedrockDataAutomationRuntimeException.php',
    'Aws\\BedrockDataAutomation\\BedrockDataAutomationClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockDataAutomation/BedrockDataAutomationClient.php',
    'Aws\\BedrockDataAutomation\\Exception\\BedrockDataAutomationException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockDataAutomation/Exception/BedrockDataAutomationException.php',
    'Aws\\BedrockRuntime\\BedrockRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/BedrockRuntime/BedrockRuntimeClient.php',
    'Aws\\BedrockRuntime\\Exception\\BedrockRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/BedrockRuntime/Exception/BedrockRuntimeException.php',
    'Aws\\Bedrock\\BedrockClient' => $vendorDir . '/aws/aws-sdk-php/src/Bedrock/BedrockClient.php',
    'Aws\\Bedrock\\Exception\\BedrockException' => $vendorDir . '/aws/aws-sdk-php/src/Bedrock/Exception/BedrockException.php',
    'Aws\\BillingConductor\\BillingConductorClient' => $vendorDir . '/aws/aws-sdk-php/src/BillingConductor/BillingConductorClient.php',
    'Aws\\BillingConductor\\Exception\\BillingConductorException' => $vendorDir . '/aws/aws-sdk-php/src/BillingConductor/Exception/BillingConductorException.php',
    'Aws\\Billing\\BillingClient' => $vendorDir . '/aws/aws-sdk-php/src/Billing/BillingClient.php',
    'Aws\\Billing\\Exception\\BillingException' => $vendorDir . '/aws/aws-sdk-php/src/Billing/Exception/BillingException.php',
    'Aws\\Braket\\BraketClient' => $vendorDir . '/aws/aws-sdk-php/src/Braket/BraketClient.php',
    'Aws\\Braket\\Exception\\BraketException' => $vendorDir . '/aws/aws-sdk-php/src/Braket/Exception/BraketException.php',
    'Aws\\Budgets\\BudgetsClient' => $vendorDir . '/aws/aws-sdk-php/src/Budgets/BudgetsClient.php',
    'Aws\\Budgets\\Exception\\BudgetsException' => $vendorDir . '/aws/aws-sdk-php/src/Budgets/Exception/BudgetsException.php',
    'Aws\\CacheInterface' => $vendorDir . '/aws/aws-sdk-php/src/CacheInterface.php',
    'Aws\\Chatbot\\ChatbotClient' => $vendorDir . '/aws/aws-sdk-php/src/Chatbot/ChatbotClient.php',
    'Aws\\Chatbot\\Exception\\ChatbotException' => $vendorDir . '/aws/aws-sdk-php/src/Chatbot/Exception/ChatbotException.php',
    'Aws\\ChimeSDKIdentity\\ChimeSDKIdentityClient' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKIdentity/ChimeSDKIdentityClient.php',
    'Aws\\ChimeSDKIdentity\\Exception\\ChimeSDKIdentityException' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKIdentity/Exception/ChimeSDKIdentityException.php',
    'Aws\\ChimeSDKMediaPipelines\\ChimeSDKMediaPipelinesClient' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMediaPipelines/ChimeSDKMediaPipelinesClient.php',
    'Aws\\ChimeSDKMediaPipelines\\Exception\\ChimeSDKMediaPipelinesException' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMediaPipelines/Exception/ChimeSDKMediaPipelinesException.php',
    'Aws\\ChimeSDKMeetings\\ChimeSDKMeetingsClient' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMeetings/ChimeSDKMeetingsClient.php',
    'Aws\\ChimeSDKMeetings\\Exception\\ChimeSDKMeetingsException' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMeetings/Exception/ChimeSDKMeetingsException.php',
    'Aws\\ChimeSDKMessaging\\ChimeSDKMessagingClient' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMessaging/ChimeSDKMessagingClient.php',
    'Aws\\ChimeSDKMessaging\\Exception\\ChimeSDKMessagingException' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKMessaging/Exception/ChimeSDKMessagingException.php',
    'Aws\\ChimeSDKVoice\\ChimeSDKVoiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKVoice/ChimeSDKVoiceClient.php',
    'Aws\\ChimeSDKVoice\\Exception\\ChimeSDKVoiceException' => $vendorDir . '/aws/aws-sdk-php/src/ChimeSDKVoice/Exception/ChimeSDKVoiceException.php',
    'Aws\\Chime\\ChimeClient' => $vendorDir . '/aws/aws-sdk-php/src/Chime/ChimeClient.php',
    'Aws\\Chime\\Exception\\ChimeException' => $vendorDir . '/aws/aws-sdk-php/src/Chime/Exception/ChimeException.php',
    'Aws\\CleanRoomsML\\CleanRoomsMLClient' => $vendorDir . '/aws/aws-sdk-php/src/CleanRoomsML/CleanRoomsMLClient.php',
    'Aws\\CleanRoomsML\\Exception\\CleanRoomsMLException' => $vendorDir . '/aws/aws-sdk-php/src/CleanRoomsML/Exception/CleanRoomsMLException.php',
    'Aws\\CleanRooms\\CleanRoomsClient' => $vendorDir . '/aws/aws-sdk-php/src/CleanRooms/CleanRoomsClient.php',
    'Aws\\CleanRooms\\Exception\\CleanRoomsException' => $vendorDir . '/aws/aws-sdk-php/src/CleanRooms/Exception/CleanRoomsException.php',
    'Aws\\ClientResolver' => $vendorDir . '/aws/aws-sdk-php/src/ClientResolver.php',
    'Aws\\ClientSideMonitoring\\AbstractMonitoringMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/AbstractMonitoringMiddleware.php',
    'Aws\\ClientSideMonitoring\\ApiCallAttemptMonitoringMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/ApiCallAttemptMonitoringMiddleware.php',
    'Aws\\ClientSideMonitoring\\ApiCallMonitoringMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/ApiCallMonitoringMiddleware.php',
    'Aws\\ClientSideMonitoring\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/Configuration.php',
    'Aws\\ClientSideMonitoring\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/ConfigurationInterface.php',
    'Aws\\ClientSideMonitoring\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/ConfigurationProvider.php',
    'Aws\\ClientSideMonitoring\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/Exception/ConfigurationException.php',
    'Aws\\ClientSideMonitoring\\MonitoringMiddlewareInterface' => $vendorDir . '/aws/aws-sdk-php/src/ClientSideMonitoring/MonitoringMiddlewareInterface.php',
    'Aws\\Cloud9\\Cloud9Client' => $vendorDir . '/aws/aws-sdk-php/src/Cloud9/Cloud9Client.php',
    'Aws\\Cloud9\\Exception\\Cloud9Exception' => $vendorDir . '/aws/aws-sdk-php/src/Cloud9/Exception/Cloud9Exception.php',
    'Aws\\CloudControlApi\\CloudControlApiClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudControlApi/CloudControlApiClient.php',
    'Aws\\CloudControlApi\\Exception\\CloudControlApiException' => $vendorDir . '/aws/aws-sdk-php/src/CloudControlApi/Exception/CloudControlApiException.php',
    'Aws\\CloudDirectory\\CloudDirectoryClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudDirectory/CloudDirectoryClient.php',
    'Aws\\CloudDirectory\\Exception\\CloudDirectoryException' => $vendorDir . '/aws/aws-sdk-php/src/CloudDirectory/Exception/CloudDirectoryException.php',
    'Aws\\CloudFormation\\CloudFormationClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudFormation/CloudFormationClient.php',
    'Aws\\CloudFormation\\Exception\\CloudFormationException' => $vendorDir . '/aws/aws-sdk-php/src/CloudFormation/Exception/CloudFormationException.php',
    'Aws\\CloudFrontKeyValueStore\\CloudFrontKeyValueStoreClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudFrontKeyValueStore/CloudFrontKeyValueStoreClient.php',
    'Aws\\CloudFrontKeyValueStore\\Exception\\CloudFrontKeyValueStoreException' => $vendorDir . '/aws/aws-sdk-php/src/CloudFrontKeyValueStore/Exception/CloudFrontKeyValueStoreException.php',
    'Aws\\CloudFront\\CloudFrontClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudFront/CloudFrontClient.php',
    'Aws\\CloudFront\\CookieSigner' => $vendorDir . '/aws/aws-sdk-php/src/CloudFront/CookieSigner.php',
    'Aws\\CloudFront\\Exception\\CloudFrontException' => $vendorDir . '/aws/aws-sdk-php/src/CloudFront/Exception/CloudFrontException.php',
    'Aws\\CloudFront\\Signer' => $vendorDir . '/aws/aws-sdk-php/src/CloudFront/Signer.php',
    'Aws\\CloudFront\\UrlSigner' => $vendorDir . '/aws/aws-sdk-php/src/CloudFront/UrlSigner.php',
    'Aws\\CloudHSMV2\\CloudHSMV2Client' => $vendorDir . '/aws/aws-sdk-php/src/CloudHSMV2/CloudHSMV2Client.php',
    'Aws\\CloudHSMV2\\Exception\\CloudHSMV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/CloudHSMV2/Exception/CloudHSMV2Exception.php',
    'Aws\\CloudHsm\\CloudHsmClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudHsm/CloudHsmClient.php',
    'Aws\\CloudHsm\\Exception\\CloudHsmException' => $vendorDir . '/aws/aws-sdk-php/src/CloudHsm/Exception/CloudHsmException.php',
    'Aws\\CloudSearchDomain\\CloudSearchDomainClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudSearchDomain/CloudSearchDomainClient.php',
    'Aws\\CloudSearchDomain\\Exception\\CloudSearchDomainException' => $vendorDir . '/aws/aws-sdk-php/src/CloudSearchDomain/Exception/CloudSearchDomainException.php',
    'Aws\\CloudSearch\\CloudSearchClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudSearch/CloudSearchClient.php',
    'Aws\\CloudSearch\\Exception\\CloudSearchException' => $vendorDir . '/aws/aws-sdk-php/src/CloudSearch/Exception/CloudSearchException.php',
    'Aws\\CloudTrailData\\CloudTrailDataClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrailData/CloudTrailDataClient.php',
    'Aws\\CloudTrailData\\Exception\\CloudTrailDataException' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrailData/Exception/CloudTrailDataException.php',
    'Aws\\CloudTrail\\CloudTrailClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrail/CloudTrailClient.php',
    'Aws\\CloudTrail\\Exception\\CloudTrailException' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrail/Exception/CloudTrailException.php',
    'Aws\\CloudTrail\\LogFileIterator' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrail/LogFileIterator.php',
    'Aws\\CloudTrail\\LogFileReader' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrail/LogFileReader.php',
    'Aws\\CloudTrail\\LogRecordIterator' => $vendorDir . '/aws/aws-sdk-php/src/CloudTrail/LogRecordIterator.php',
    'Aws\\CloudWatchEvents\\CloudWatchEventsClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchEvents/CloudWatchEventsClient.php',
    'Aws\\CloudWatchEvents\\Exception\\CloudWatchEventsException' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchEvents/Exception/CloudWatchEventsException.php',
    'Aws\\CloudWatchEvidently\\CloudWatchEvidentlyClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchEvidently/CloudWatchEvidentlyClient.php',
    'Aws\\CloudWatchEvidently\\Exception\\CloudWatchEvidentlyException' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchEvidently/Exception/CloudWatchEvidentlyException.php',
    'Aws\\CloudWatchLogs\\CloudWatchLogsClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchLogs/CloudWatchLogsClient.php',
    'Aws\\CloudWatchLogs\\Exception\\CloudWatchLogsException' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchLogs/Exception/CloudWatchLogsException.php',
    'Aws\\CloudWatchRUM\\CloudWatchRUMClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchRUM/CloudWatchRUMClient.php',
    'Aws\\CloudWatchRUM\\Exception\\CloudWatchRUMException' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatchRUM/Exception/CloudWatchRUMException.php',
    'Aws\\CloudWatch\\CloudWatchClient' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatch/CloudWatchClient.php',
    'Aws\\CloudWatch\\Exception\\CloudWatchException' => $vendorDir . '/aws/aws-sdk-php/src/CloudWatch/Exception/CloudWatchException.php',
    'Aws\\CodeArtifact\\CodeArtifactClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeArtifact/CodeArtifactClient.php',
    'Aws\\CodeArtifact\\Exception\\CodeArtifactException' => $vendorDir . '/aws/aws-sdk-php/src/CodeArtifact/Exception/CodeArtifactException.php',
    'Aws\\CodeBuild\\CodeBuildClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeBuild/CodeBuildClient.php',
    'Aws\\CodeBuild\\Exception\\CodeBuildException' => $vendorDir . '/aws/aws-sdk-php/src/CodeBuild/Exception/CodeBuildException.php',
    'Aws\\CodeCatalyst\\CodeCatalystClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeCatalyst/CodeCatalystClient.php',
    'Aws\\CodeCatalyst\\Exception\\CodeCatalystException' => $vendorDir . '/aws/aws-sdk-php/src/CodeCatalyst/Exception/CodeCatalystException.php',
    'Aws\\CodeCommit\\CodeCommitClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeCommit/CodeCommitClient.php',
    'Aws\\CodeCommit\\Exception\\CodeCommitException' => $vendorDir . '/aws/aws-sdk-php/src/CodeCommit/Exception/CodeCommitException.php',
    'Aws\\CodeConnections\\CodeConnectionsClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeConnections/CodeConnectionsClient.php',
    'Aws\\CodeConnections\\Exception\\CodeConnectionsException' => $vendorDir . '/aws/aws-sdk-php/src/CodeConnections/Exception/CodeConnectionsException.php',
    'Aws\\CodeDeploy\\CodeDeployClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeDeploy/CodeDeployClient.php',
    'Aws\\CodeDeploy\\Exception\\CodeDeployException' => $vendorDir . '/aws/aws-sdk-php/src/CodeDeploy/Exception/CodeDeployException.php',
    'Aws\\CodeGuruProfiler\\CodeGuruProfilerClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruProfiler/CodeGuruProfilerClient.php',
    'Aws\\CodeGuruProfiler\\Exception\\CodeGuruProfilerException' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruProfiler/Exception/CodeGuruProfilerException.php',
    'Aws\\CodeGuruReviewer\\CodeGuruReviewerClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruReviewer/CodeGuruReviewerClient.php',
    'Aws\\CodeGuruReviewer\\Exception\\CodeGuruReviewerException' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruReviewer/Exception/CodeGuruReviewerException.php',
    'Aws\\CodeGuruSecurity\\CodeGuruSecurityClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruSecurity/CodeGuruSecurityClient.php',
    'Aws\\CodeGuruSecurity\\Exception\\CodeGuruSecurityException' => $vendorDir . '/aws/aws-sdk-php/src/CodeGuruSecurity/Exception/CodeGuruSecurityException.php',
    'Aws\\CodePipeline\\CodePipelineClient' => $vendorDir . '/aws/aws-sdk-php/src/CodePipeline/CodePipelineClient.php',
    'Aws\\CodePipeline\\Exception\\CodePipelineException' => $vendorDir . '/aws/aws-sdk-php/src/CodePipeline/Exception/CodePipelineException.php',
    'Aws\\CodeStarNotifications\\CodeStarNotificationsClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeStarNotifications/CodeStarNotificationsClient.php',
    'Aws\\CodeStarNotifications\\Exception\\CodeStarNotificationsException' => $vendorDir . '/aws/aws-sdk-php/src/CodeStarNotifications/Exception/CodeStarNotificationsException.php',
    'Aws\\CodeStarconnections\\CodeStarconnectionsClient' => $vendorDir . '/aws/aws-sdk-php/src/CodeStarconnections/CodeStarconnectionsClient.php',
    'Aws\\CodeStarconnections\\Exception\\CodeStarconnectionsException' => $vendorDir . '/aws/aws-sdk-php/src/CodeStarconnections/Exception/CodeStarconnectionsException.php',
    'Aws\\CognitoIdentityProvider\\CognitoIdentityProviderClient' => $vendorDir . '/aws/aws-sdk-php/src/CognitoIdentityProvider/CognitoIdentityProviderClient.php',
    'Aws\\CognitoIdentityProvider\\Exception\\CognitoIdentityProviderException' => $vendorDir . '/aws/aws-sdk-php/src/CognitoIdentityProvider/Exception/CognitoIdentityProviderException.php',
    'Aws\\CognitoIdentity\\CognitoIdentityClient' => $vendorDir . '/aws/aws-sdk-php/src/CognitoIdentity/CognitoIdentityClient.php',
    'Aws\\CognitoIdentity\\CognitoIdentityProvider' => $vendorDir . '/aws/aws-sdk-php/src/CognitoIdentity/CognitoIdentityProvider.php',
    'Aws\\CognitoIdentity\\Exception\\CognitoIdentityException' => $vendorDir . '/aws/aws-sdk-php/src/CognitoIdentity/Exception/CognitoIdentityException.php',
    'Aws\\CognitoSync\\CognitoSyncClient' => $vendorDir . '/aws/aws-sdk-php/src/CognitoSync/CognitoSyncClient.php',
    'Aws\\CognitoSync\\Exception\\CognitoSyncException' => $vendorDir . '/aws/aws-sdk-php/src/CognitoSync/Exception/CognitoSyncException.php',
    'Aws\\Command' => $vendorDir . '/aws/aws-sdk-php/src/Command.php',
    'Aws\\CommandInterface' => $vendorDir . '/aws/aws-sdk-php/src/CommandInterface.php',
    'Aws\\CommandPool' => $vendorDir . '/aws/aws-sdk-php/src/CommandPool.php',
    'Aws\\ComprehendMedical\\ComprehendMedicalClient' => $vendorDir . '/aws/aws-sdk-php/src/ComprehendMedical/ComprehendMedicalClient.php',
    'Aws\\ComprehendMedical\\Exception\\ComprehendMedicalException' => $vendorDir . '/aws/aws-sdk-php/src/ComprehendMedical/Exception/ComprehendMedicalException.php',
    'Aws\\Comprehend\\ComprehendClient' => $vendorDir . '/aws/aws-sdk-php/src/Comprehend/ComprehendClient.php',
    'Aws\\Comprehend\\Exception\\ComprehendException' => $vendorDir . '/aws/aws-sdk-php/src/Comprehend/Exception/ComprehendException.php',
    'Aws\\ComputeOptimizer\\ComputeOptimizerClient' => $vendorDir . '/aws/aws-sdk-php/src/ComputeOptimizer/ComputeOptimizerClient.php',
    'Aws\\ComputeOptimizer\\Exception\\ComputeOptimizerException' => $vendorDir . '/aws/aws-sdk-php/src/ComputeOptimizer/Exception/ComputeOptimizerException.php',
    'Aws\\ConfigService\\ConfigServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ConfigService/ConfigServiceClient.php',
    'Aws\\ConfigService\\Exception\\ConfigServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ConfigService/Exception/ConfigServiceException.php',
    'Aws\\ConfigurationProviderInterface' => $vendorDir . '/aws/aws-sdk-php/src/ConfigurationProviderInterface.php',
    'Aws\\Configuration\\ConfigurationResolver' => $vendorDir . '/aws/aws-sdk-php/src/Configuration/ConfigurationResolver.php',
    'Aws\\ConnectCampaignService\\ConnectCampaignServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCampaignService/ConnectCampaignServiceClient.php',
    'Aws\\ConnectCampaignService\\Exception\\ConnectCampaignServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCampaignService/Exception/ConnectCampaignServiceException.php',
    'Aws\\ConnectCampaignsV2\\ConnectCampaignsV2Client' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCampaignsV2/ConnectCampaignsV2Client.php',
    'Aws\\ConnectCampaignsV2\\Exception\\ConnectCampaignsV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCampaignsV2/Exception/ConnectCampaignsV2Exception.php',
    'Aws\\ConnectCases\\ConnectCasesClient' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCases/ConnectCasesClient.php',
    'Aws\\ConnectCases\\Exception\\ConnectCasesException' => $vendorDir . '/aws/aws-sdk-php/src/ConnectCases/Exception/ConnectCasesException.php',
    'Aws\\ConnectContactLens\\ConnectContactLensClient' => $vendorDir . '/aws/aws-sdk-php/src/ConnectContactLens/ConnectContactLensClient.php',
    'Aws\\ConnectContactLens\\Exception\\ConnectContactLensException' => $vendorDir . '/aws/aws-sdk-php/src/ConnectContactLens/Exception/ConnectContactLensException.php',
    'Aws\\ConnectParticipant\\ConnectParticipantClient' => $vendorDir . '/aws/aws-sdk-php/src/ConnectParticipant/ConnectParticipantClient.php',
    'Aws\\ConnectParticipant\\Exception\\ConnectParticipantException' => $vendorDir . '/aws/aws-sdk-php/src/ConnectParticipant/Exception/ConnectParticipantException.php',
    'Aws\\ConnectWisdomService\\ConnectWisdomServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ConnectWisdomService/ConnectWisdomServiceClient.php',
    'Aws\\ConnectWisdomService\\Exception\\ConnectWisdomServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ConnectWisdomService/Exception/ConnectWisdomServiceException.php',
    'Aws\\Connect\\ConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/Connect/ConnectClient.php',
    'Aws\\Connect\\Exception\\ConnectException' => $vendorDir . '/aws/aws-sdk-php/src/Connect/Exception/ConnectException.php',
    'Aws\\ControlCatalog\\ControlCatalogClient' => $vendorDir . '/aws/aws-sdk-php/src/ControlCatalog/ControlCatalogClient.php',
    'Aws\\ControlCatalog\\Exception\\ControlCatalogException' => $vendorDir . '/aws/aws-sdk-php/src/ControlCatalog/Exception/ControlCatalogException.php',
    'Aws\\ControlTower\\ControlTowerClient' => $vendorDir . '/aws/aws-sdk-php/src/ControlTower/ControlTowerClient.php',
    'Aws\\ControlTower\\Exception\\ControlTowerException' => $vendorDir . '/aws/aws-sdk-php/src/ControlTower/Exception/ControlTowerException.php',
    'Aws\\CostExplorer\\CostExplorerClient' => $vendorDir . '/aws/aws-sdk-php/src/CostExplorer/CostExplorerClient.php',
    'Aws\\CostExplorer\\Exception\\CostExplorerException' => $vendorDir . '/aws/aws-sdk-php/src/CostExplorer/Exception/CostExplorerException.php',
    'Aws\\CostOptimizationHub\\CostOptimizationHubClient' => $vendorDir . '/aws/aws-sdk-php/src/CostOptimizationHub/CostOptimizationHubClient.php',
    'Aws\\CostOptimizationHub\\Exception\\CostOptimizationHubException' => $vendorDir . '/aws/aws-sdk-php/src/CostOptimizationHub/Exception/CostOptimizationHubException.php',
    'Aws\\CostandUsageReportService\\CostandUsageReportServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/CostandUsageReportService/CostandUsageReportServiceClient.php',
    'Aws\\CostandUsageReportService\\Exception\\CostandUsageReportServiceException' => $vendorDir . '/aws/aws-sdk-php/src/CostandUsageReportService/Exception/CostandUsageReportServiceException.php',
    'Aws\\Credentials\\AssumeRoleCredentialProvider' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/AssumeRoleCredentialProvider.php',
    'Aws\\Credentials\\AssumeRoleWithWebIdentityCredentialProvider' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/AssumeRoleWithWebIdentityCredentialProvider.php',
    'Aws\\Credentials\\CredentialProvider' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/CredentialProvider.php',
    'Aws\\Credentials\\CredentialSources' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/CredentialSources.php',
    'Aws\\Credentials\\Credentials' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/Credentials.php',
    'Aws\\Credentials\\CredentialsInterface' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/CredentialsInterface.php',
    'Aws\\Credentials\\CredentialsUtils' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/CredentialsUtils.php',
    'Aws\\Credentials\\EcsCredentialProvider' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/EcsCredentialProvider.php',
    'Aws\\Credentials\\InstanceProfileProvider' => $vendorDir . '/aws/aws-sdk-php/src/Credentials/InstanceProfileProvider.php',
    'Aws\\Crypto\\AbstractCryptoClient' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AbstractCryptoClient.php',
    'Aws\\Crypto\\AbstractCryptoClientV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AbstractCryptoClientV2.php',
    'Aws\\Crypto\\AesDecryptingStream' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesDecryptingStream.php',
    'Aws\\Crypto\\AesEncryptingStream' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesEncryptingStream.php',
    'Aws\\Crypto\\AesGcmDecryptingStream' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesGcmDecryptingStream.php',
    'Aws\\Crypto\\AesGcmEncryptingStream' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesGcmEncryptingStream.php',
    'Aws\\Crypto\\AesStreamInterface' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesStreamInterface.php',
    'Aws\\Crypto\\AesStreamInterfaceV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/AesStreamInterfaceV2.php',
    'Aws\\Crypto\\Cipher\\Cbc' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/Cipher/Cbc.php',
    'Aws\\Crypto\\Cipher\\CipherBuilderTrait' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/Cipher/CipherBuilderTrait.php',
    'Aws\\Crypto\\Cipher\\CipherMethod' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/Cipher/CipherMethod.php',
    'Aws\\Crypto\\DecryptionTrait' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/DecryptionTrait.php',
    'Aws\\Crypto\\DecryptionTraitV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/DecryptionTraitV2.php',
    'Aws\\Crypto\\EncryptionTrait' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/EncryptionTrait.php',
    'Aws\\Crypto\\EncryptionTraitV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/EncryptionTraitV2.php',
    'Aws\\Crypto\\KmsMaterialsProvider' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/KmsMaterialsProvider.php',
    'Aws\\Crypto\\KmsMaterialsProviderV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/KmsMaterialsProviderV2.php',
    'Aws\\Crypto\\MaterialsProvider' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MaterialsProvider.php',
    'Aws\\Crypto\\MaterialsProviderInterface' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MaterialsProviderInterface.php',
    'Aws\\Crypto\\MaterialsProviderInterfaceV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MaterialsProviderInterfaceV2.php',
    'Aws\\Crypto\\MaterialsProviderV2' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MaterialsProviderV2.php',
    'Aws\\Crypto\\MetadataEnvelope' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MetadataEnvelope.php',
    'Aws\\Crypto\\MetadataStrategyInterface' => $vendorDir . '/aws/aws-sdk-php/src/Crypto/MetadataStrategyInterface.php',
    'Aws\\CustomerProfiles\\CustomerProfilesClient' => $vendorDir . '/aws/aws-sdk-php/src/CustomerProfiles/CustomerProfilesClient.php',
    'Aws\\CustomerProfiles\\Exception\\CustomerProfilesException' => $vendorDir . '/aws/aws-sdk-php/src/CustomerProfiles/Exception/CustomerProfilesException.php',
    'Aws\\DAX\\DAXClient' => $vendorDir . '/aws/aws-sdk-php/src/DAX/DAXClient.php',
    'Aws\\DAX\\Exception\\DAXException' => $vendorDir . '/aws/aws-sdk-php/src/DAX/Exception/DAXException.php',
    'Aws\\DLM\\DLMClient' => $vendorDir . '/aws/aws-sdk-php/src/DLM/DLMClient.php',
    'Aws\\DLM\\Exception\\DLMException' => $vendorDir . '/aws/aws-sdk-php/src/DLM/Exception/DLMException.php',
    'Aws\\DSQL\\AuthTokenGenerator' => $vendorDir . '/aws/aws-sdk-php/src/DSQL/AuthTokenGenerator.php',
    'Aws\\DSQL\\DSQLClient' => $vendorDir . '/aws/aws-sdk-php/src/DSQL/DSQLClient.php',
    'Aws\\DSQL\\Exception\\DSQLException' => $vendorDir . '/aws/aws-sdk-php/src/DSQL/Exception/DSQLException.php',
    'Aws\\DataExchange\\DataExchangeClient' => $vendorDir . '/aws/aws-sdk-php/src/DataExchange/DataExchangeClient.php',
    'Aws\\DataExchange\\Exception\\DataExchangeException' => $vendorDir . '/aws/aws-sdk-php/src/DataExchange/Exception/DataExchangeException.php',
    'Aws\\DataPipeline\\DataPipelineClient' => $vendorDir . '/aws/aws-sdk-php/src/DataPipeline/DataPipelineClient.php',
    'Aws\\DataPipeline\\Exception\\DataPipelineException' => $vendorDir . '/aws/aws-sdk-php/src/DataPipeline/Exception/DataPipelineException.php',
    'Aws\\DataSync\\DataSyncClient' => $vendorDir . '/aws/aws-sdk-php/src/DataSync/DataSyncClient.php',
    'Aws\\DataSync\\Exception\\DataSyncException' => $vendorDir . '/aws/aws-sdk-php/src/DataSync/Exception/DataSyncException.php',
    'Aws\\DataZone\\DataZoneClient' => $vendorDir . '/aws/aws-sdk-php/src/DataZone/DataZoneClient.php',
    'Aws\\DataZone\\Exception\\DataZoneException' => $vendorDir . '/aws/aws-sdk-php/src/DataZone/Exception/DataZoneException.php',
    'Aws\\DatabaseMigrationService\\DatabaseMigrationServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/DatabaseMigrationService/DatabaseMigrationServiceClient.php',
    'Aws\\DatabaseMigrationService\\Exception\\DatabaseMigrationServiceException' => $vendorDir . '/aws/aws-sdk-php/src/DatabaseMigrationService/Exception/DatabaseMigrationServiceException.php',
    'Aws\\Deadline\\DeadlineClient' => $vendorDir . '/aws/aws-sdk-php/src/Deadline/DeadlineClient.php',
    'Aws\\Deadline\\Exception\\DeadlineException' => $vendorDir . '/aws/aws-sdk-php/src/Deadline/Exception/DeadlineException.php',
    'Aws\\DefaultsMode\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/DefaultsMode/Configuration.php',
    'Aws\\DefaultsMode\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/DefaultsMode/ConfigurationInterface.php',
    'Aws\\DefaultsMode\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/DefaultsMode/ConfigurationProvider.php',
    'Aws\\DefaultsMode\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/DefaultsMode/Exception/ConfigurationException.php',
    'Aws\\Detective\\DetectiveClient' => $vendorDir . '/aws/aws-sdk-php/src/Detective/DetectiveClient.php',
    'Aws\\Detective\\Exception\\DetectiveException' => $vendorDir . '/aws/aws-sdk-php/src/Detective/Exception/DetectiveException.php',
    'Aws\\DevOpsGuru\\DevOpsGuruClient' => $vendorDir . '/aws/aws-sdk-php/src/DevOpsGuru/DevOpsGuruClient.php',
    'Aws\\DevOpsGuru\\Exception\\DevOpsGuruException' => $vendorDir . '/aws/aws-sdk-php/src/DevOpsGuru/Exception/DevOpsGuruException.php',
    'Aws\\DeviceFarm\\DeviceFarmClient' => $vendorDir . '/aws/aws-sdk-php/src/DeviceFarm/DeviceFarmClient.php',
    'Aws\\DeviceFarm\\Exception\\DeviceFarmException' => $vendorDir . '/aws/aws-sdk-php/src/DeviceFarm/Exception/DeviceFarmException.php',
    'Aws\\DirectConnect\\DirectConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/DirectConnect/DirectConnectClient.php',
    'Aws\\DirectConnect\\Exception\\DirectConnectException' => $vendorDir . '/aws/aws-sdk-php/src/DirectConnect/Exception/DirectConnectException.php',
    'Aws\\DirectoryServiceData\\DirectoryServiceDataClient' => $vendorDir . '/aws/aws-sdk-php/src/DirectoryServiceData/DirectoryServiceDataClient.php',
    'Aws\\DirectoryServiceData\\Exception\\DirectoryServiceDataException' => $vendorDir . '/aws/aws-sdk-php/src/DirectoryServiceData/Exception/DirectoryServiceDataException.php',
    'Aws\\DirectoryService\\DirectoryServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/DirectoryService/DirectoryServiceClient.php',
    'Aws\\DirectoryService\\Exception\\DirectoryServiceException' => $vendorDir . '/aws/aws-sdk-php/src/DirectoryService/Exception/DirectoryServiceException.php',
    'Aws\\DocDBElastic\\DocDBElasticClient' => $vendorDir . '/aws/aws-sdk-php/src/DocDBElastic/DocDBElasticClient.php',
    'Aws\\DocDBElastic\\Exception\\DocDBElasticException' => $vendorDir . '/aws/aws-sdk-php/src/DocDBElastic/Exception/DocDBElasticException.php',
    'Aws\\DocDB\\DocDBClient' => $vendorDir . '/aws/aws-sdk-php/src/DocDB/DocDBClient.php',
    'Aws\\DocDB\\Exception\\DocDBException' => $vendorDir . '/aws/aws-sdk-php/src/DocDB/Exception/DocDBException.php',
    'Aws\\DoctrineCacheAdapter' => $vendorDir . '/aws/aws-sdk-php/src/DoctrineCacheAdapter.php',
    'Aws\\DynamoDbStreams\\DynamoDbStreamsClient' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDbStreams/DynamoDbStreamsClient.php',
    'Aws\\DynamoDbStreams\\Exception\\DynamoDbStreamsException' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDbStreams/Exception/DynamoDbStreamsException.php',
    'Aws\\DynamoDb\\BinaryValue' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/BinaryValue.php',
    'Aws\\DynamoDb\\DynamoDbClient' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/DynamoDbClient.php',
    'Aws\\DynamoDb\\Exception\\DynamoDbException' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/Exception/DynamoDbException.php',
    'Aws\\DynamoDb\\LockingSessionConnection' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/LockingSessionConnection.php',
    'Aws\\DynamoDb\\Marshaler' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/Marshaler.php',
    'Aws\\DynamoDb\\NumberValue' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/NumberValue.php',
    'Aws\\DynamoDb\\SessionConnectionConfigTrait' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/SessionConnectionConfigTrait.php',
    'Aws\\DynamoDb\\SessionConnectionInterface' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/SessionConnectionInterface.php',
    'Aws\\DynamoDb\\SessionHandler' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/SessionHandler.php',
    'Aws\\DynamoDb\\SetValue' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/SetValue.php',
    'Aws\\DynamoDb\\StandardSessionConnection' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/StandardSessionConnection.php',
    'Aws\\DynamoDb\\WriteRequestBatch' => $vendorDir . '/aws/aws-sdk-php/src/DynamoDb/WriteRequestBatch.php',
    'Aws\\EBS\\EBSClient' => $vendorDir . '/aws/aws-sdk-php/src/EBS/EBSClient.php',
    'Aws\\EBS\\Exception\\EBSException' => $vendorDir . '/aws/aws-sdk-php/src/EBS/Exception/EBSException.php',
    'Aws\\EC2InstanceConnect\\EC2InstanceConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/EC2InstanceConnect/EC2InstanceConnectClient.php',
    'Aws\\EC2InstanceConnect\\Exception\\EC2InstanceConnectException' => $vendorDir . '/aws/aws-sdk-php/src/EC2InstanceConnect/Exception/EC2InstanceConnectException.php',
    'Aws\\ECRPublic\\ECRPublicClient' => $vendorDir . '/aws/aws-sdk-php/src/ECRPublic/ECRPublicClient.php',
    'Aws\\ECRPublic\\Exception\\ECRPublicException' => $vendorDir . '/aws/aws-sdk-php/src/ECRPublic/Exception/ECRPublicException.php',
    'Aws\\EKSAuth\\EKSAuthClient' => $vendorDir . '/aws/aws-sdk-php/src/EKSAuth/EKSAuthClient.php',
    'Aws\\EKSAuth\\Exception\\EKSAuthException' => $vendorDir . '/aws/aws-sdk-php/src/EKSAuth/Exception/EKSAuthException.php',
    'Aws\\EKS\\EKSClient' => $vendorDir . '/aws/aws-sdk-php/src/EKS/EKSClient.php',
    'Aws\\EKS\\Exception\\EKSException' => $vendorDir . '/aws/aws-sdk-php/src/EKS/Exception/EKSException.php',
    'Aws\\EMRContainers\\EMRContainersClient' => $vendorDir . '/aws/aws-sdk-php/src/EMRContainers/EMRContainersClient.php',
    'Aws\\EMRContainers\\Exception\\EMRContainersException' => $vendorDir . '/aws/aws-sdk-php/src/EMRContainers/Exception/EMRContainersException.php',
    'Aws\\EMRServerless\\EMRServerlessClient' => $vendorDir . '/aws/aws-sdk-php/src/EMRServerless/EMRServerlessClient.php',
    'Aws\\EMRServerless\\Exception\\EMRServerlessException' => $vendorDir . '/aws/aws-sdk-php/src/EMRServerless/Exception/EMRServerlessException.php',
    'Aws\\Ec2\\Ec2Client' => $vendorDir . '/aws/aws-sdk-php/src/Ec2/Ec2Client.php',
    'Aws\\Ec2\\Exception\\Ec2Exception' => $vendorDir . '/aws/aws-sdk-php/src/Ec2/Exception/Ec2Exception.php',
    'Aws\\Ecr\\EcrClient' => $vendorDir . '/aws/aws-sdk-php/src/Ecr/EcrClient.php',
    'Aws\\Ecr\\Exception\\EcrException' => $vendorDir . '/aws/aws-sdk-php/src/Ecr/Exception/EcrException.php',
    'Aws\\Ecs\\EcsClient' => $vendorDir . '/aws/aws-sdk-php/src/Ecs/EcsClient.php',
    'Aws\\Ecs\\Exception\\EcsException' => $vendorDir . '/aws/aws-sdk-php/src/Ecs/Exception/EcsException.php',
    'Aws\\Efs\\EfsClient' => $vendorDir . '/aws/aws-sdk-php/src/Efs/EfsClient.php',
    'Aws\\Efs\\Exception\\EfsException' => $vendorDir . '/aws/aws-sdk-php/src/Efs/Exception/EfsException.php',
    'Aws\\ElastiCache\\ElastiCacheClient' => $vendorDir . '/aws/aws-sdk-php/src/ElastiCache/ElastiCacheClient.php',
    'Aws\\ElastiCache\\Exception\\ElastiCacheException' => $vendorDir . '/aws/aws-sdk-php/src/ElastiCache/Exception/ElastiCacheException.php',
    'Aws\\ElasticBeanstalk\\ElasticBeanstalkClient' => $vendorDir . '/aws/aws-sdk-php/src/ElasticBeanstalk/ElasticBeanstalkClient.php',
    'Aws\\ElasticBeanstalk\\Exception\\ElasticBeanstalkException' => $vendorDir . '/aws/aws-sdk-php/src/ElasticBeanstalk/Exception/ElasticBeanstalkException.php',
    'Aws\\ElasticLoadBalancingV2\\ElasticLoadBalancingV2Client' => $vendorDir . '/aws/aws-sdk-php/src/ElasticLoadBalancingV2/ElasticLoadBalancingV2Client.php',
    'Aws\\ElasticLoadBalancingV2\\Exception\\ElasticLoadBalancingV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/ElasticLoadBalancingV2/Exception/ElasticLoadBalancingV2Exception.php',
    'Aws\\ElasticLoadBalancing\\ElasticLoadBalancingClient' => $vendorDir . '/aws/aws-sdk-php/src/ElasticLoadBalancing/ElasticLoadBalancingClient.php',
    'Aws\\ElasticLoadBalancing\\Exception\\ElasticLoadBalancingException' => $vendorDir . '/aws/aws-sdk-php/src/ElasticLoadBalancing/Exception/ElasticLoadBalancingException.php',
    'Aws\\ElasticTranscoder\\ElasticTranscoderClient' => $vendorDir . '/aws/aws-sdk-php/src/ElasticTranscoder/ElasticTranscoderClient.php',
    'Aws\\ElasticTranscoder\\Exception\\ElasticTranscoderException' => $vendorDir . '/aws/aws-sdk-php/src/ElasticTranscoder/Exception/ElasticTranscoderException.php',
    'Aws\\ElasticsearchService\\ElasticsearchServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ElasticsearchService/ElasticsearchServiceClient.php',
    'Aws\\ElasticsearchService\\Exception\\ElasticsearchServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ElasticsearchService/Exception/ElasticsearchServiceException.php',
    'Aws\\Emr\\EmrClient' => $vendorDir . '/aws/aws-sdk-php/src/Emr/EmrClient.php',
    'Aws\\Emr\\Exception\\EmrException' => $vendorDir . '/aws/aws-sdk-php/src/Emr/Exception/EmrException.php',
    'Aws\\EndpointDiscovery\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/Configuration.php',
    'Aws\\EndpointDiscovery\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/ConfigurationInterface.php',
    'Aws\\EndpointDiscovery\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/ConfigurationProvider.php',
    'Aws\\EndpointDiscovery\\EndpointDiscoveryMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/EndpointDiscoveryMiddleware.php',
    'Aws\\EndpointDiscovery\\EndpointList' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/EndpointList.php',
    'Aws\\EndpointDiscovery\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/EndpointDiscovery/Exception/ConfigurationException.php',
    'Aws\\EndpointParameterMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/EndpointParameterMiddleware.php',
    'Aws\\EndpointV2\\EndpointDefinitionProvider' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/EndpointDefinitionProvider.php',
    'Aws\\EndpointV2\\EndpointProviderV2' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/EndpointProviderV2.php',
    'Aws\\EndpointV2\\EndpointV2Middleware' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/EndpointV2Middleware.php',
    'Aws\\EndpointV2\\EndpointV2SerializerTrait' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/EndpointV2SerializerTrait.php',
    'Aws\\EndpointV2\\Rule\\AbstractRule' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Rule/AbstractRule.php',
    'Aws\\EndpointV2\\Rule\\EndpointRule' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Rule/EndpointRule.php',
    'Aws\\EndpointV2\\Rule\\ErrorRule' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Rule/ErrorRule.php',
    'Aws\\EndpointV2\\Rule\\RuleCreator' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Rule/RuleCreator.php',
    'Aws\\EndpointV2\\Rule\\TreeRule' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Rule/TreeRule.php',
    'Aws\\EndpointV2\\Ruleset\\Ruleset' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Ruleset/Ruleset.php',
    'Aws\\EndpointV2\\Ruleset\\RulesetEndpoint' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Ruleset/RulesetEndpoint.php',
    'Aws\\EndpointV2\\Ruleset\\RulesetParameter' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Ruleset/RulesetParameter.php',
    'Aws\\EndpointV2\\Ruleset\\RulesetStandardLibrary' => $vendorDir . '/aws/aws-sdk-php/src/EndpointV2/Ruleset/RulesetStandardLibrary.php',
    'Aws\\Endpoint\\EndpointProvider' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/EndpointProvider.php',
    'Aws\\Endpoint\\Partition' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/Partition.php',
    'Aws\\Endpoint\\PartitionEndpointProvider' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/PartitionEndpointProvider.php',
    'Aws\\Endpoint\\PartitionInterface' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/PartitionInterface.php',
    'Aws\\Endpoint\\PatternEndpointProvider' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/PatternEndpointProvider.php',
    'Aws\\Endpoint\\UseDualstackEndpoint\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseDualstackEndpoint/Configuration.php',
    'Aws\\Endpoint\\UseDualstackEndpoint\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseDualstackEndpoint/ConfigurationInterface.php',
    'Aws\\Endpoint\\UseDualstackEndpoint\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseDualstackEndpoint/ConfigurationProvider.php',
    'Aws\\Endpoint\\UseDualstackEndpoint\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseDualstackEndpoint/Exception/ConfigurationException.php',
    'Aws\\Endpoint\\UseFipsEndpoint\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseFipsEndpoint/Configuration.php',
    'Aws\\Endpoint\\UseFipsEndpoint\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseFipsEndpoint/ConfigurationInterface.php',
    'Aws\\Endpoint\\UseFipsEndpoint\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseFipsEndpoint/ConfigurationProvider.php',
    'Aws\\Endpoint\\UseFipsEndpoint\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/Endpoint/UseFipsEndpoint/Exception/ConfigurationException.php',
    'Aws\\EntityResolution\\EntityResolutionClient' => $vendorDir . '/aws/aws-sdk-php/src/EntityResolution/EntityResolutionClient.php',
    'Aws\\EntityResolution\\Exception\\EntityResolutionException' => $vendorDir . '/aws/aws-sdk-php/src/EntityResolution/Exception/EntityResolutionException.php',
    'Aws\\EventBridge\\EventBridgeClient' => $vendorDir . '/aws/aws-sdk-php/src/EventBridge/EventBridgeClient.php',
    'Aws\\EventBridge\\EventBridgeEndpointMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/EventBridge/EventBridgeEndpointMiddleware.php',
    'Aws\\EventBridge\\Exception\\EventBridgeException' => $vendorDir . '/aws/aws-sdk-php/src/EventBridge/Exception/EventBridgeException.php',
    'Aws\\Evs\\EvsClient' => $vendorDir . '/aws/aws-sdk-php/src/Evs/EvsClient.php',
    'Aws\\Evs\\Exception\\EvsException' => $vendorDir . '/aws/aws-sdk-php/src/Evs/Exception/EvsException.php',
    'Aws\\Exception\\AwsException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/AwsException.php',
    'Aws\\Exception\\CommonRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/CommonRuntimeException.php',
    'Aws\\Exception\\CouldNotCreateChecksumException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/CouldNotCreateChecksumException.php',
    'Aws\\Exception\\CredentialsException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/CredentialsException.php',
    'Aws\\Exception\\CryptoException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/CryptoException.php',
    'Aws\\Exception\\CryptoPolyfillException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/CryptoPolyfillException.php',
    'Aws\\Exception\\EventStreamDataException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/EventStreamDataException.php',
    'Aws\\Exception\\IncalculablePayloadException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/IncalculablePayloadException.php',
    'Aws\\Exception\\InvalidJsonException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/InvalidJsonException.php',
    'Aws\\Exception\\InvalidRegionException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/InvalidRegionException.php',
    'Aws\\Exception\\MultipartUploadException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/MultipartUploadException.php',
    'Aws\\Exception\\TokenException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/TokenException.php',
    'Aws\\Exception\\UnresolvedApiException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/UnresolvedApiException.php',
    'Aws\\Exception\\UnresolvedEndpointException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/UnresolvedEndpointException.php',
    'Aws\\Exception\\UnresolvedSignatureException' => $vendorDir . '/aws/aws-sdk-php/src/Exception/UnresolvedSignatureException.php',
    'Aws\\FIS\\Exception\\FISException' => $vendorDir . '/aws/aws-sdk-php/src/FIS/Exception/FISException.php',
    'Aws\\FIS\\FISClient' => $vendorDir . '/aws/aws-sdk-php/src/FIS/FISClient.php',
    'Aws\\FMS\\Exception\\FMSException' => $vendorDir . '/aws/aws-sdk-php/src/FMS/Exception/FMSException.php',
    'Aws\\FMS\\FMSClient' => $vendorDir . '/aws/aws-sdk-php/src/FMS/FMSClient.php',
    'Aws\\FSx\\Exception\\FSxException' => $vendorDir . '/aws/aws-sdk-php/src/FSx/Exception/FSxException.php',
    'Aws\\FSx\\FSxClient' => $vendorDir . '/aws/aws-sdk-php/src/FSx/FSxClient.php',
    'Aws\\FinSpaceData\\Exception\\FinSpaceDataException' => $vendorDir . '/aws/aws-sdk-php/src/FinSpaceData/Exception/FinSpaceDataException.php',
    'Aws\\FinSpaceData\\FinSpaceDataClient' => $vendorDir . '/aws/aws-sdk-php/src/FinSpaceData/FinSpaceDataClient.php',
    'Aws\\Firehose\\Exception\\FirehoseException' => $vendorDir . '/aws/aws-sdk-php/src/Firehose/Exception/FirehoseException.php',
    'Aws\\Firehose\\FirehoseClient' => $vendorDir . '/aws/aws-sdk-php/src/Firehose/FirehoseClient.php',
    'Aws\\ForecastQueryService\\Exception\\ForecastQueryServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ForecastQueryService/Exception/ForecastQueryServiceException.php',
    'Aws\\ForecastQueryService\\ForecastQueryServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ForecastQueryService/ForecastQueryServiceClient.php',
    'Aws\\ForecastService\\Exception\\ForecastServiceException' => $vendorDir . '/aws/aws-sdk-php/src/ForecastService/Exception/ForecastServiceException.php',
    'Aws\\ForecastService\\ForecastServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/ForecastService/ForecastServiceClient.php',
    'Aws\\FraudDetector\\Exception\\FraudDetectorException' => $vendorDir . '/aws/aws-sdk-php/src/FraudDetector/Exception/FraudDetectorException.php',
    'Aws\\FraudDetector\\FraudDetectorClient' => $vendorDir . '/aws/aws-sdk-php/src/FraudDetector/FraudDetectorClient.php',
    'Aws\\FreeTier\\Exception\\FreeTierException' => $vendorDir . '/aws/aws-sdk-php/src/FreeTier/Exception/FreeTierException.php',
    'Aws\\FreeTier\\FreeTierClient' => $vendorDir . '/aws/aws-sdk-php/src/FreeTier/FreeTierClient.php',
    'Aws\\GameLiftStreams\\Exception\\GameLiftStreamsException' => $vendorDir . '/aws/aws-sdk-php/src/GameLiftStreams/Exception/GameLiftStreamsException.php',
    'Aws\\GameLiftStreams\\GameLiftStreamsClient' => $vendorDir . '/aws/aws-sdk-php/src/GameLiftStreams/GameLiftStreamsClient.php',
    'Aws\\GameLift\\Exception\\GameLiftException' => $vendorDir . '/aws/aws-sdk-php/src/GameLift/Exception/GameLiftException.php',
    'Aws\\GameLift\\GameLiftClient' => $vendorDir . '/aws/aws-sdk-php/src/GameLift/GameLiftClient.php',
    'Aws\\GeoMaps\\Exception\\GeoMapsException' => $vendorDir . '/aws/aws-sdk-php/src/GeoMaps/Exception/GeoMapsException.php',
    'Aws\\GeoMaps\\GeoMapsClient' => $vendorDir . '/aws/aws-sdk-php/src/GeoMaps/GeoMapsClient.php',
    'Aws\\GeoPlaces\\Exception\\GeoPlacesException' => $vendorDir . '/aws/aws-sdk-php/src/GeoPlaces/Exception/GeoPlacesException.php',
    'Aws\\GeoPlaces\\GeoPlacesClient' => $vendorDir . '/aws/aws-sdk-php/src/GeoPlaces/GeoPlacesClient.php',
    'Aws\\GeoRoutes\\Exception\\GeoRoutesException' => $vendorDir . '/aws/aws-sdk-php/src/GeoRoutes/Exception/GeoRoutesException.php',
    'Aws\\GeoRoutes\\GeoRoutesClient' => $vendorDir . '/aws/aws-sdk-php/src/GeoRoutes/GeoRoutesClient.php',
    'Aws\\Glacier\\Exception\\GlacierException' => $vendorDir . '/aws/aws-sdk-php/src/Glacier/Exception/GlacierException.php',
    'Aws\\Glacier\\GlacierClient' => $vendorDir . '/aws/aws-sdk-php/src/Glacier/GlacierClient.php',
    'Aws\\Glacier\\MultipartUploader' => $vendorDir . '/aws/aws-sdk-php/src/Glacier/MultipartUploader.php',
    'Aws\\Glacier\\TreeHash' => $vendorDir . '/aws/aws-sdk-php/src/Glacier/TreeHash.php',
    'Aws\\GlobalAccelerator\\Exception\\GlobalAcceleratorException' => $vendorDir . '/aws/aws-sdk-php/src/GlobalAccelerator/Exception/GlobalAcceleratorException.php',
    'Aws\\GlobalAccelerator\\GlobalAcceleratorClient' => $vendorDir . '/aws/aws-sdk-php/src/GlobalAccelerator/GlobalAcceleratorClient.php',
    'Aws\\GlueDataBrew\\Exception\\GlueDataBrewException' => $vendorDir . '/aws/aws-sdk-php/src/GlueDataBrew/Exception/GlueDataBrewException.php',
    'Aws\\GlueDataBrew\\GlueDataBrewClient' => $vendorDir . '/aws/aws-sdk-php/src/GlueDataBrew/GlueDataBrewClient.php',
    'Aws\\Glue\\Exception\\GlueException' => $vendorDir . '/aws/aws-sdk-php/src/Glue/Exception/GlueException.php',
    'Aws\\Glue\\GlueClient' => $vendorDir . '/aws/aws-sdk-php/src/Glue/GlueClient.php',
    'Aws\\GreengrassV2\\Exception\\GreengrassV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/GreengrassV2/Exception/GreengrassV2Exception.php',
    'Aws\\GreengrassV2\\GreengrassV2Client' => $vendorDir . '/aws/aws-sdk-php/src/GreengrassV2/GreengrassV2Client.php',
    'Aws\\Greengrass\\Exception\\GreengrassException' => $vendorDir . '/aws/aws-sdk-php/src/Greengrass/Exception/GreengrassException.php',
    'Aws\\Greengrass\\GreengrassClient' => $vendorDir . '/aws/aws-sdk-php/src/Greengrass/GreengrassClient.php',
    'Aws\\GroundStation\\Exception\\GroundStationException' => $vendorDir . '/aws/aws-sdk-php/src/GroundStation/Exception/GroundStationException.php',
    'Aws\\GroundStation\\GroundStationClient' => $vendorDir . '/aws/aws-sdk-php/src/GroundStation/GroundStationClient.php',
    'Aws\\GuardDuty\\Exception\\GuardDutyException' => $vendorDir . '/aws/aws-sdk-php/src/GuardDuty/Exception/GuardDutyException.php',
    'Aws\\GuardDuty\\GuardDutyClient' => $vendorDir . '/aws/aws-sdk-php/src/GuardDuty/GuardDutyClient.php',
    'Aws\\HandlerList' => $vendorDir . '/aws/aws-sdk-php/src/HandlerList.php',
    'Aws\\Handler\\Guzzle\\GuzzleHandler' => $vendorDir . '/aws/aws-sdk-php/src/Handler/Guzzle/GuzzleHandler.php',
    'Aws\\HasDataTrait' => $vendorDir . '/aws/aws-sdk-php/src/HasDataTrait.php',
    'Aws\\HasMonitoringEventsTrait' => $vendorDir . '/aws/aws-sdk-php/src/HasMonitoringEventsTrait.php',
    'Aws\\HashInterface' => $vendorDir . '/aws/aws-sdk-php/src/HashInterface.php',
    'Aws\\HashingStream' => $vendorDir . '/aws/aws-sdk-php/src/HashingStream.php',
    'Aws\\HealthLake\\Exception\\HealthLakeException' => $vendorDir . '/aws/aws-sdk-php/src/HealthLake/Exception/HealthLakeException.php',
    'Aws\\HealthLake\\HealthLakeClient' => $vendorDir . '/aws/aws-sdk-php/src/HealthLake/HealthLakeClient.php',
    'Aws\\Health\\Exception\\HealthException' => $vendorDir . '/aws/aws-sdk-php/src/Health/Exception/HealthException.php',
    'Aws\\Health\\HealthClient' => $vendorDir . '/aws/aws-sdk-php/src/Health/HealthClient.php',
    'Aws\\History' => $vendorDir . '/aws/aws-sdk-php/src/History.php',
    'Aws\\IVSRealTime\\Exception\\IVSRealTimeException' => $vendorDir . '/aws/aws-sdk-php/src/IVSRealTime/Exception/IVSRealTimeException.php',
    'Aws\\IVSRealTime\\IVSRealTimeClient' => $vendorDir . '/aws/aws-sdk-php/src/IVSRealTime/IVSRealTimeClient.php',
    'Aws\\IVS\\Exception\\IVSException' => $vendorDir . '/aws/aws-sdk-php/src/IVS/Exception/IVSException.php',
    'Aws\\IVS\\IVSClient' => $vendorDir . '/aws/aws-sdk-php/src/IVS/IVSClient.php',
    'Aws\\Iam\\Exception\\IamException' => $vendorDir . '/aws/aws-sdk-php/src/Iam/Exception/IamException.php',
    'Aws\\Iam\\IamClient' => $vendorDir . '/aws/aws-sdk-php/src/Iam/IamClient.php',
    'Aws\\IdempotencyTokenMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/IdempotencyTokenMiddleware.php',
    'Aws\\IdentityStore\\Exception\\IdentityStoreException' => $vendorDir . '/aws/aws-sdk-php/src/IdentityStore/Exception/IdentityStoreException.php',
    'Aws\\IdentityStore\\IdentityStoreClient' => $vendorDir . '/aws/aws-sdk-php/src/IdentityStore/IdentityStoreClient.php',
    'Aws\\Identity\\AwsCredentialIdentity' => $vendorDir . '/aws/aws-sdk-php/src/Identity/AwsCredentialIdentity.php',
    'Aws\\Identity\\BearerTokenIdentity' => $vendorDir . '/aws/aws-sdk-php/src/Identity/BearerTokenIdentity.php',
    'Aws\\Identity\\IdentityInterface' => $vendorDir . '/aws/aws-sdk-php/src/Identity/IdentityInterface.php',
    'Aws\\Identity\\S3\\S3ExpressIdentity' => $vendorDir . '/aws/aws-sdk-php/src/Identity/S3/S3ExpressIdentity.php',
    'Aws\\Identity\\S3\\S3ExpressIdentityProvider' => $vendorDir . '/aws/aws-sdk-php/src/Identity/S3/S3ExpressIdentityProvider.php',
    'Aws\\ImportExport\\Exception\\ImportExportException' => $vendorDir . '/aws/aws-sdk-php/src/ImportExport/Exception/ImportExportException.php',
    'Aws\\ImportExport\\ImportExportClient' => $vendorDir . '/aws/aws-sdk-php/src/ImportExport/ImportExportClient.php',
    'Aws\\InputValidationMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/InputValidationMiddleware.php',
    'Aws\\Inspector2\\Exception\\Inspector2Exception' => $vendorDir . '/aws/aws-sdk-php/src/Inspector2/Exception/Inspector2Exception.php',
    'Aws\\Inspector2\\Inspector2Client' => $vendorDir . '/aws/aws-sdk-php/src/Inspector2/Inspector2Client.php',
    'Aws\\InspectorScan\\Exception\\InspectorScanException' => $vendorDir . '/aws/aws-sdk-php/src/InspectorScan/Exception/InspectorScanException.php',
    'Aws\\InspectorScan\\InspectorScanClient' => $vendorDir . '/aws/aws-sdk-php/src/InspectorScan/InspectorScanClient.php',
    'Aws\\Inspector\\Exception\\InspectorException' => $vendorDir . '/aws/aws-sdk-php/src/Inspector/Exception/InspectorException.php',
    'Aws\\Inspector\\InspectorClient' => $vendorDir . '/aws/aws-sdk-php/src/Inspector/InspectorClient.php',
    'Aws\\InternetMonitor\\Exception\\InternetMonitorException' => $vendorDir . '/aws/aws-sdk-php/src/InternetMonitor/Exception/InternetMonitorException.php',
    'Aws\\InternetMonitor\\InternetMonitorClient' => $vendorDir . '/aws/aws-sdk-php/src/InternetMonitor/InternetMonitorClient.php',
    'Aws\\Invoicing\\Exception\\InvoicingException' => $vendorDir . '/aws/aws-sdk-php/src/Invoicing/Exception/InvoicingException.php',
    'Aws\\Invoicing\\InvoicingClient' => $vendorDir . '/aws/aws-sdk-php/src/Invoicing/InvoicingClient.php',
    'Aws\\IoTAnalytics\\Exception\\IoTAnalyticsException' => $vendorDir . '/aws/aws-sdk-php/src/IoTAnalytics/Exception/IoTAnalyticsException.php',
    'Aws\\IoTAnalytics\\IoTAnalyticsClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTAnalytics/IoTAnalyticsClient.php',
    'Aws\\IoTDeviceAdvisor\\Exception\\IoTDeviceAdvisorException' => $vendorDir . '/aws/aws-sdk-php/src/IoTDeviceAdvisor/Exception/IoTDeviceAdvisorException.php',
    'Aws\\IoTDeviceAdvisor\\IoTDeviceAdvisorClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTDeviceAdvisor/IoTDeviceAdvisorClient.php',
    'Aws\\IoTEventsData\\Exception\\IoTEventsDataException' => $vendorDir . '/aws/aws-sdk-php/src/IoTEventsData/Exception/IoTEventsDataException.php',
    'Aws\\IoTEventsData\\IoTEventsDataClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTEventsData/IoTEventsDataClient.php',
    'Aws\\IoTEvents\\Exception\\IoTEventsException' => $vendorDir . '/aws/aws-sdk-php/src/IoTEvents/Exception/IoTEventsException.php',
    'Aws\\IoTEvents\\IoTEventsClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTEvents/IoTEventsClient.php',
    'Aws\\IoTFleetHub\\Exception\\IoTFleetHubException' => $vendorDir . '/aws/aws-sdk-php/src/IoTFleetHub/Exception/IoTFleetHubException.php',
    'Aws\\IoTFleetHub\\IoTFleetHubClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTFleetHub/IoTFleetHubClient.php',
    'Aws\\IoTFleetWise\\Exception\\IoTFleetWiseException' => $vendorDir . '/aws/aws-sdk-php/src/IoTFleetWise/Exception/IoTFleetWiseException.php',
    'Aws\\IoTFleetWise\\IoTFleetWiseClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTFleetWise/IoTFleetWiseClient.php',
    'Aws\\IoTJobsDataPlane\\Exception\\IoTJobsDataPlaneException' => $vendorDir . '/aws/aws-sdk-php/src/IoTJobsDataPlane/Exception/IoTJobsDataPlaneException.php',
    'Aws\\IoTJobsDataPlane\\IoTJobsDataPlaneClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTJobsDataPlane/IoTJobsDataPlaneClient.php',
    'Aws\\IoTManagedIntegrations\\Exception\\IoTManagedIntegrationsException' => $vendorDir . '/aws/aws-sdk-php/src/IoTManagedIntegrations/Exception/IoTManagedIntegrationsException.php',
    'Aws\\IoTManagedIntegrations\\IoTManagedIntegrationsClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTManagedIntegrations/IoTManagedIntegrationsClient.php',
    'Aws\\IoTSecureTunneling\\Exception\\IoTSecureTunnelingException' => $vendorDir . '/aws/aws-sdk-php/src/IoTSecureTunneling/Exception/IoTSecureTunnelingException.php',
    'Aws\\IoTSecureTunneling\\IoTSecureTunnelingClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTSecureTunneling/IoTSecureTunnelingClient.php',
    'Aws\\IoTSiteWise\\Exception\\IoTSiteWiseException' => $vendorDir . '/aws/aws-sdk-php/src/IoTSiteWise/Exception/IoTSiteWiseException.php',
    'Aws\\IoTSiteWise\\IoTSiteWiseClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTSiteWise/IoTSiteWiseClient.php',
    'Aws\\IoTThingsGraph\\Exception\\IoTThingsGraphException' => $vendorDir . '/aws/aws-sdk-php/src/IoTThingsGraph/Exception/IoTThingsGraphException.php',
    'Aws\\IoTThingsGraph\\IoTThingsGraphClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTThingsGraph/IoTThingsGraphClient.php',
    'Aws\\IoTTwinMaker\\Exception\\IoTTwinMakerException' => $vendorDir . '/aws/aws-sdk-php/src/IoTTwinMaker/Exception/IoTTwinMakerException.php',
    'Aws\\IoTTwinMaker\\IoTTwinMakerClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTTwinMaker/IoTTwinMakerClient.php',
    'Aws\\IoTWireless\\Exception\\IoTWirelessException' => $vendorDir . '/aws/aws-sdk-php/src/IoTWireless/Exception/IoTWirelessException.php',
    'Aws\\IoTWireless\\IoTWirelessClient' => $vendorDir . '/aws/aws-sdk-php/src/IoTWireless/IoTWirelessClient.php',
    'Aws\\IotDataPlane\\Exception\\IotDataPlaneException' => $vendorDir . '/aws/aws-sdk-php/src/IotDataPlane/Exception/IotDataPlaneException.php',
    'Aws\\IotDataPlane\\IotDataPlaneClient' => $vendorDir . '/aws/aws-sdk-php/src/IotDataPlane/IotDataPlaneClient.php',
    'Aws\\Iot\\Exception\\IotException' => $vendorDir . '/aws/aws-sdk-php/src/Iot/Exception/IotException.php',
    'Aws\\Iot\\IotClient' => $vendorDir . '/aws/aws-sdk-php/src/Iot/IotClient.php',
    'Aws\\JsonCompiler' => $vendorDir . '/aws/aws-sdk-php/src/JsonCompiler.php',
    'Aws\\KafkaConnect\\Exception\\KafkaConnectException' => $vendorDir . '/aws/aws-sdk-php/src/KafkaConnect/Exception/KafkaConnectException.php',
    'Aws\\KafkaConnect\\KafkaConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/KafkaConnect/KafkaConnectClient.php',
    'Aws\\Kafka\\Exception\\KafkaException' => $vendorDir . '/aws/aws-sdk-php/src/Kafka/Exception/KafkaException.php',
    'Aws\\Kafka\\KafkaClient' => $vendorDir . '/aws/aws-sdk-php/src/Kafka/KafkaClient.php',
    'Aws\\KendraRanking\\Exception\\KendraRankingException' => $vendorDir . '/aws/aws-sdk-php/src/KendraRanking/Exception/KendraRankingException.php',
    'Aws\\KendraRanking\\KendraRankingClient' => $vendorDir . '/aws/aws-sdk-php/src/KendraRanking/KendraRankingClient.php',
    'Aws\\KeyspacesStreams\\Exception\\KeyspacesStreamsException' => $vendorDir . '/aws/aws-sdk-php/src/KeyspacesStreams/Exception/KeyspacesStreamsException.php',
    'Aws\\KeyspacesStreams\\KeyspacesStreamsClient' => $vendorDir . '/aws/aws-sdk-php/src/KeyspacesStreams/KeyspacesStreamsClient.php',
    'Aws\\Keyspaces\\Exception\\KeyspacesException' => $vendorDir . '/aws/aws-sdk-php/src/Keyspaces/Exception/KeyspacesException.php',
    'Aws\\Keyspaces\\KeyspacesClient' => $vendorDir . '/aws/aws-sdk-php/src/Keyspaces/KeyspacesClient.php',
    'Aws\\KinesisAnalyticsV2\\Exception\\KinesisAnalyticsV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/KinesisAnalyticsV2/Exception/KinesisAnalyticsV2Exception.php',
    'Aws\\KinesisAnalyticsV2\\KinesisAnalyticsV2Client' => $vendorDir . '/aws/aws-sdk-php/src/KinesisAnalyticsV2/KinesisAnalyticsV2Client.php',
    'Aws\\KinesisAnalytics\\Exception\\KinesisAnalyticsException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisAnalytics/Exception/KinesisAnalyticsException.php',
    'Aws\\KinesisAnalytics\\KinesisAnalyticsClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisAnalytics/KinesisAnalyticsClient.php',
    'Aws\\KinesisVideoArchivedMedia\\Exception\\KinesisVideoArchivedMediaException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoArchivedMedia/Exception/KinesisVideoArchivedMediaException.php',
    'Aws\\KinesisVideoArchivedMedia\\KinesisVideoArchivedMediaClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoArchivedMedia/KinesisVideoArchivedMediaClient.php',
    'Aws\\KinesisVideoMedia\\Exception\\KinesisVideoMediaException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoMedia/Exception/KinesisVideoMediaException.php',
    'Aws\\KinesisVideoMedia\\KinesisVideoMediaClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoMedia/KinesisVideoMediaClient.php',
    'Aws\\KinesisVideoSignalingChannels\\Exception\\KinesisVideoSignalingChannelsException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoSignalingChannels/Exception/KinesisVideoSignalingChannelsException.php',
    'Aws\\KinesisVideoSignalingChannels\\KinesisVideoSignalingChannelsClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoSignalingChannels/KinesisVideoSignalingChannelsClient.php',
    'Aws\\KinesisVideoWebRTCStorage\\Exception\\KinesisVideoWebRTCStorageException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoWebRTCStorage/Exception/KinesisVideoWebRTCStorageException.php',
    'Aws\\KinesisVideoWebRTCStorage\\KinesisVideoWebRTCStorageClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideoWebRTCStorage/KinesisVideoWebRTCStorageClient.php',
    'Aws\\KinesisVideo\\Exception\\KinesisVideoException' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideo/Exception/KinesisVideoException.php',
    'Aws\\KinesisVideo\\KinesisVideoClient' => $vendorDir . '/aws/aws-sdk-php/src/KinesisVideo/KinesisVideoClient.php',
    'Aws\\Kinesis\\Exception\\KinesisException' => $vendorDir . '/aws/aws-sdk-php/src/Kinesis/Exception/KinesisException.php',
    'Aws\\Kinesis\\KinesisClient' => $vendorDir . '/aws/aws-sdk-php/src/Kinesis/KinesisClient.php',
    'Aws\\Kms\\Exception\\KmsException' => $vendorDir . '/aws/aws-sdk-php/src/Kms/Exception/KmsException.php',
    'Aws\\Kms\\KmsClient' => $vendorDir . '/aws/aws-sdk-php/src/Kms/KmsClient.php',
    'Aws\\LakeFormation\\Exception\\LakeFormationException' => $vendorDir . '/aws/aws-sdk-php/src/LakeFormation/Exception/LakeFormationException.php',
    'Aws\\LakeFormation\\LakeFormationClient' => $vendorDir . '/aws/aws-sdk-php/src/LakeFormation/LakeFormationClient.php',
    'Aws\\Lambda\\Exception\\LambdaException' => $vendorDir . '/aws/aws-sdk-php/src/Lambda/Exception/LambdaException.php',
    'Aws\\Lambda\\LambdaClient' => $vendorDir . '/aws/aws-sdk-php/src/Lambda/LambdaClient.php',
    'Aws\\LaunchWizard\\Exception\\LaunchWizardException' => $vendorDir . '/aws/aws-sdk-php/src/LaunchWizard/Exception/LaunchWizardException.php',
    'Aws\\LaunchWizard\\LaunchWizardClient' => $vendorDir . '/aws/aws-sdk-php/src/LaunchWizard/LaunchWizardClient.php',
    'Aws\\LexModelBuildingService\\Exception\\LexModelBuildingServiceException' => $vendorDir . '/aws/aws-sdk-php/src/LexModelBuildingService/Exception/LexModelBuildingServiceException.php',
    'Aws\\LexModelBuildingService\\LexModelBuildingServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/LexModelBuildingService/LexModelBuildingServiceClient.php',
    'Aws\\LexModelsV2\\Exception\\LexModelsV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/LexModelsV2/Exception/LexModelsV2Exception.php',
    'Aws\\LexModelsV2\\LexModelsV2Client' => $vendorDir . '/aws/aws-sdk-php/src/LexModelsV2/LexModelsV2Client.php',
    'Aws\\LexRuntimeService\\Exception\\LexRuntimeServiceException' => $vendorDir . '/aws/aws-sdk-php/src/LexRuntimeService/Exception/LexRuntimeServiceException.php',
    'Aws\\LexRuntimeService\\LexRuntimeServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/LexRuntimeService/LexRuntimeServiceClient.php',
    'Aws\\LexRuntimeV2\\Exception\\LexRuntimeV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/LexRuntimeV2/Exception/LexRuntimeV2Exception.php',
    'Aws\\LexRuntimeV2\\LexRuntimeV2Client' => $vendorDir . '/aws/aws-sdk-php/src/LexRuntimeV2/LexRuntimeV2Client.php',
    'Aws\\LicenseManagerLinuxSubscriptions\\Exception\\LicenseManagerLinuxSubscriptionsException' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManagerLinuxSubscriptions/Exception/LicenseManagerLinuxSubscriptionsException.php',
    'Aws\\LicenseManagerLinuxSubscriptions\\LicenseManagerLinuxSubscriptionsClient' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManagerLinuxSubscriptions/LicenseManagerLinuxSubscriptionsClient.php',
    'Aws\\LicenseManagerUserSubscriptions\\Exception\\LicenseManagerUserSubscriptionsException' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManagerUserSubscriptions/Exception/LicenseManagerUserSubscriptionsException.php',
    'Aws\\LicenseManagerUserSubscriptions\\LicenseManagerUserSubscriptionsClient' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManagerUserSubscriptions/LicenseManagerUserSubscriptionsClient.php',
    'Aws\\LicenseManager\\Exception\\LicenseManagerException' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManager/Exception/LicenseManagerException.php',
    'Aws\\LicenseManager\\LicenseManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/LicenseManager/LicenseManagerClient.php',
    'Aws\\Lightsail\\Exception\\LightsailException' => $vendorDir . '/aws/aws-sdk-php/src/Lightsail/Exception/LightsailException.php',
    'Aws\\Lightsail\\LightsailClient' => $vendorDir . '/aws/aws-sdk-php/src/Lightsail/LightsailClient.php',
    'Aws\\LocationService\\Exception\\LocationServiceException' => $vendorDir . '/aws/aws-sdk-php/src/LocationService/Exception/LocationServiceException.php',
    'Aws\\LocationService\\LocationServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/LocationService/LocationServiceClient.php',
    'Aws\\LookoutEquipment\\Exception\\LookoutEquipmentException' => $vendorDir . '/aws/aws-sdk-php/src/LookoutEquipment/Exception/LookoutEquipmentException.php',
    'Aws\\LookoutEquipment\\LookoutEquipmentClient' => $vendorDir . '/aws/aws-sdk-php/src/LookoutEquipment/LookoutEquipmentClient.php',
    'Aws\\LookoutMetrics\\Exception\\LookoutMetricsException' => $vendorDir . '/aws/aws-sdk-php/src/LookoutMetrics/Exception/LookoutMetricsException.php',
    'Aws\\LookoutMetrics\\LookoutMetricsClient' => $vendorDir . '/aws/aws-sdk-php/src/LookoutMetrics/LookoutMetricsClient.php',
    'Aws\\LookoutforVision\\Exception\\LookoutforVisionException' => $vendorDir . '/aws/aws-sdk-php/src/LookoutforVision/Exception/LookoutforVisionException.php',
    'Aws\\LookoutforVision\\LookoutforVisionClient' => $vendorDir . '/aws/aws-sdk-php/src/LookoutforVision/LookoutforVisionClient.php',
    'Aws\\LruArrayCache' => $vendorDir . '/aws/aws-sdk-php/src/LruArrayCache.php',
    'Aws\\MPA\\Exception\\MPAException' => $vendorDir . '/aws/aws-sdk-php/src/MPA/Exception/MPAException.php',
    'Aws\\MPA\\MPAClient' => $vendorDir . '/aws/aws-sdk-php/src/MPA/MPAClient.php',
    'Aws\\MQ\\Exception\\MQException' => $vendorDir . '/aws/aws-sdk-php/src/MQ/Exception/MQException.php',
    'Aws\\MQ\\MQClient' => $vendorDir . '/aws/aws-sdk-php/src/MQ/MQClient.php',
    'Aws\\MTurk\\Exception\\MTurkException' => $vendorDir . '/aws/aws-sdk-php/src/MTurk/Exception/MTurkException.php',
    'Aws\\MTurk\\MTurkClient' => $vendorDir . '/aws/aws-sdk-php/src/MTurk/MTurkClient.php',
    'Aws\\MWAA\\Exception\\MWAAException' => $vendorDir . '/aws/aws-sdk-php/src/MWAA/Exception/MWAAException.php',
    'Aws\\MWAA\\MWAAClient' => $vendorDir . '/aws/aws-sdk-php/src/MWAA/MWAAClient.php',
    'Aws\\MachineLearning\\Exception\\MachineLearningException' => $vendorDir . '/aws/aws-sdk-php/src/MachineLearning/Exception/MachineLearningException.php',
    'Aws\\MachineLearning\\MachineLearningClient' => $vendorDir . '/aws/aws-sdk-php/src/MachineLearning/MachineLearningClient.php',
    'Aws\\Macie2\\Exception\\Macie2Exception' => $vendorDir . '/aws/aws-sdk-php/src/Macie2/Exception/Macie2Exception.php',
    'Aws\\Macie2\\Macie2Client' => $vendorDir . '/aws/aws-sdk-php/src/Macie2/Macie2Client.php',
    'Aws\\MailManager\\Exception\\MailManagerException' => $vendorDir . '/aws/aws-sdk-php/src/MailManager/Exception/MailManagerException.php',
    'Aws\\MailManager\\MailManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/MailManager/MailManagerClient.php',
    'Aws\\MainframeModernization\\Exception\\MainframeModernizationException' => $vendorDir . '/aws/aws-sdk-php/src/MainframeModernization/Exception/MainframeModernizationException.php',
    'Aws\\MainframeModernization\\MainframeModernizationClient' => $vendorDir . '/aws/aws-sdk-php/src/MainframeModernization/MainframeModernizationClient.php',
    'Aws\\ManagedBlockchainQuery\\Exception\\ManagedBlockchainQueryException' => $vendorDir . '/aws/aws-sdk-php/src/ManagedBlockchainQuery/Exception/ManagedBlockchainQueryException.php',
    'Aws\\ManagedBlockchainQuery\\ManagedBlockchainQueryClient' => $vendorDir . '/aws/aws-sdk-php/src/ManagedBlockchainQuery/ManagedBlockchainQueryClient.php',
    'Aws\\ManagedBlockchain\\Exception\\ManagedBlockchainException' => $vendorDir . '/aws/aws-sdk-php/src/ManagedBlockchain/Exception/ManagedBlockchainException.php',
    'Aws\\ManagedBlockchain\\ManagedBlockchainClient' => $vendorDir . '/aws/aws-sdk-php/src/ManagedBlockchain/ManagedBlockchainClient.php',
    'Aws\\ManagedGrafana\\Exception\\ManagedGrafanaException' => $vendorDir . '/aws/aws-sdk-php/src/ManagedGrafana/Exception/ManagedGrafanaException.php',
    'Aws\\ManagedGrafana\\ManagedGrafanaClient' => $vendorDir . '/aws/aws-sdk-php/src/ManagedGrafana/ManagedGrafanaClient.php',
    'Aws\\MarketplaceAgreement\\Exception\\MarketplaceAgreementException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceAgreement/Exception/MarketplaceAgreementException.php',
    'Aws\\MarketplaceAgreement\\MarketplaceAgreementClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceAgreement/MarketplaceAgreementClient.php',
    'Aws\\MarketplaceCatalog\\Exception\\MarketplaceCatalogException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceCatalog/Exception/MarketplaceCatalogException.php',
    'Aws\\MarketplaceCatalog\\MarketplaceCatalogClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceCatalog/MarketplaceCatalogClient.php',
    'Aws\\MarketplaceCommerceAnalytics\\Exception\\MarketplaceCommerceAnalyticsException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceCommerceAnalytics/Exception/MarketplaceCommerceAnalyticsException.php',
    'Aws\\MarketplaceCommerceAnalytics\\MarketplaceCommerceAnalyticsClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceCommerceAnalytics/MarketplaceCommerceAnalyticsClient.php',
    'Aws\\MarketplaceDeployment\\Exception\\MarketplaceDeploymentException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceDeployment/Exception/MarketplaceDeploymentException.php',
    'Aws\\MarketplaceDeployment\\MarketplaceDeploymentClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceDeployment/MarketplaceDeploymentClient.php',
    'Aws\\MarketplaceEntitlementService\\Exception\\MarketplaceEntitlementServiceException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceEntitlementService/Exception/MarketplaceEntitlementServiceException.php',
    'Aws\\MarketplaceEntitlementService\\MarketplaceEntitlementServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceEntitlementService/MarketplaceEntitlementServiceClient.php',
    'Aws\\MarketplaceMetering\\Exception\\MarketplaceMeteringException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceMetering/Exception/MarketplaceMeteringException.php',
    'Aws\\MarketplaceMetering\\MarketplaceMeteringClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceMetering/MarketplaceMeteringClient.php',
    'Aws\\MarketplaceReporting\\Exception\\MarketplaceReportingException' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceReporting/Exception/MarketplaceReportingException.php',
    'Aws\\MarketplaceReporting\\MarketplaceReportingClient' => $vendorDir . '/aws/aws-sdk-php/src/MarketplaceReporting/MarketplaceReportingClient.php',
    'Aws\\MediaConnect\\Exception\\MediaConnectException' => $vendorDir . '/aws/aws-sdk-php/src/MediaConnect/Exception/MediaConnectException.php',
    'Aws\\MediaConnect\\MediaConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaConnect/MediaConnectClient.php',
    'Aws\\MediaConvert\\Exception\\MediaConvertException' => $vendorDir . '/aws/aws-sdk-php/src/MediaConvert/Exception/MediaConvertException.php',
    'Aws\\MediaConvert\\MediaConvertClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaConvert/MediaConvertClient.php',
    'Aws\\MediaLive\\Exception\\MediaLiveException' => $vendorDir . '/aws/aws-sdk-php/src/MediaLive/Exception/MediaLiveException.php',
    'Aws\\MediaLive\\MediaLiveClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaLive/MediaLiveClient.php',
    'Aws\\MediaPackageV2\\Exception\\MediaPackageV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackageV2/Exception/MediaPackageV2Exception.php',
    'Aws\\MediaPackageV2\\MediaPackageV2Client' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackageV2/MediaPackageV2Client.php',
    'Aws\\MediaPackageVod\\Exception\\MediaPackageVodException' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackageVod/Exception/MediaPackageVodException.php',
    'Aws\\MediaPackageVod\\MediaPackageVodClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackageVod/MediaPackageVodClient.php',
    'Aws\\MediaPackage\\Exception\\MediaPackageException' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackage/Exception/MediaPackageException.php',
    'Aws\\MediaPackage\\MediaPackageClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaPackage/MediaPackageClient.php',
    'Aws\\MediaStoreData\\Exception\\MediaStoreDataException' => $vendorDir . '/aws/aws-sdk-php/src/MediaStoreData/Exception/MediaStoreDataException.php',
    'Aws\\MediaStoreData\\MediaStoreDataClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaStoreData/MediaStoreDataClient.php',
    'Aws\\MediaStore\\Exception\\MediaStoreException' => $vendorDir . '/aws/aws-sdk-php/src/MediaStore/Exception/MediaStoreException.php',
    'Aws\\MediaStore\\MediaStoreClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaStore/MediaStoreClient.php',
    'Aws\\MediaTailor\\Exception\\MediaTailorException' => $vendorDir . '/aws/aws-sdk-php/src/MediaTailor/Exception/MediaTailorException.php',
    'Aws\\MediaTailor\\MediaTailorClient' => $vendorDir . '/aws/aws-sdk-php/src/MediaTailor/MediaTailorClient.php',
    'Aws\\MedicalImaging\\Exception\\MedicalImagingException' => $vendorDir . '/aws/aws-sdk-php/src/MedicalImaging/Exception/MedicalImagingException.php',
    'Aws\\MedicalImaging\\MedicalImagingClient' => $vendorDir . '/aws/aws-sdk-php/src/MedicalImaging/MedicalImagingClient.php',
    'Aws\\MemoryDB\\Exception\\MemoryDBException' => $vendorDir . '/aws/aws-sdk-php/src/MemoryDB/Exception/MemoryDBException.php',
    'Aws\\MemoryDB\\MemoryDBClient' => $vendorDir . '/aws/aws-sdk-php/src/MemoryDB/MemoryDBClient.php',
    'Aws\\MetricsBuilder' => $vendorDir . '/aws/aws-sdk-php/src/MetricsBuilder.php',
    'Aws\\Middleware' => $vendorDir . '/aws/aws-sdk-php/src/Middleware.php',
    'Aws\\MigrationHubConfig\\Exception\\MigrationHubConfigException' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubConfig/Exception/MigrationHubConfigException.php',
    'Aws\\MigrationHubConfig\\MigrationHubConfigClient' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubConfig/MigrationHubConfigClient.php',
    'Aws\\MigrationHubOrchestrator\\Exception\\MigrationHubOrchestratorException' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubOrchestrator/Exception/MigrationHubOrchestratorException.php',
    'Aws\\MigrationHubOrchestrator\\MigrationHubOrchestratorClient' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubOrchestrator/MigrationHubOrchestratorClient.php',
    'Aws\\MigrationHubRefactorSpaces\\Exception\\MigrationHubRefactorSpacesException' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubRefactorSpaces/Exception/MigrationHubRefactorSpacesException.php',
    'Aws\\MigrationHubRefactorSpaces\\MigrationHubRefactorSpacesClient' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubRefactorSpaces/MigrationHubRefactorSpacesClient.php',
    'Aws\\MigrationHubStrategyRecommendations\\Exception\\MigrationHubStrategyRecommendationsException' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubStrategyRecommendations/Exception/MigrationHubStrategyRecommendationsException.php',
    'Aws\\MigrationHubStrategyRecommendations\\MigrationHubStrategyRecommendationsClient' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHubStrategyRecommendations/MigrationHubStrategyRecommendationsClient.php',
    'Aws\\MigrationHub\\Exception\\MigrationHubException' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHub/Exception/MigrationHubException.php',
    'Aws\\MigrationHub\\MigrationHubClient' => $vendorDir . '/aws/aws-sdk-php/src/MigrationHub/MigrationHubClient.php',
    'Aws\\MockHandler' => $vendorDir . '/aws/aws-sdk-php/src/MockHandler.php',
    'Aws\\MonitoringEventsInterface' => $vendorDir . '/aws/aws-sdk-php/src/MonitoringEventsInterface.php',
    'Aws\\MultiRegionClient' => $vendorDir . '/aws/aws-sdk-php/src/MultiRegionClient.php',
    'Aws\\Multipart\\AbstractUploadManager' => $vendorDir . '/aws/aws-sdk-php/src/Multipart/AbstractUploadManager.php',
    'Aws\\Multipart\\AbstractUploader' => $vendorDir . '/aws/aws-sdk-php/src/Multipart/AbstractUploader.php',
    'Aws\\Multipart\\UploadState' => $vendorDir . '/aws/aws-sdk-php/src/Multipart/UploadState.php',
    'Aws\\NeptuneGraph\\Exception\\NeptuneGraphException' => $vendorDir . '/aws/aws-sdk-php/src/NeptuneGraph/Exception/NeptuneGraphException.php',
    'Aws\\NeptuneGraph\\NeptuneGraphClient' => $vendorDir . '/aws/aws-sdk-php/src/NeptuneGraph/NeptuneGraphClient.php',
    'Aws\\Neptune\\Exception\\NeptuneException' => $vendorDir . '/aws/aws-sdk-php/src/Neptune/Exception/NeptuneException.php',
    'Aws\\Neptune\\NeptuneClient' => $vendorDir . '/aws/aws-sdk-php/src/Neptune/NeptuneClient.php',
    'Aws\\Neptunedata\\Exception\\NeptunedataException' => $vendorDir . '/aws/aws-sdk-php/src/Neptunedata/Exception/NeptunedataException.php',
    'Aws\\Neptunedata\\NeptunedataClient' => $vendorDir . '/aws/aws-sdk-php/src/Neptunedata/NeptunedataClient.php',
    'Aws\\NetworkFirewall\\Exception\\NetworkFirewallException' => $vendorDir . '/aws/aws-sdk-php/src/NetworkFirewall/Exception/NetworkFirewallException.php',
    'Aws\\NetworkFirewall\\NetworkFirewallClient' => $vendorDir . '/aws/aws-sdk-php/src/NetworkFirewall/NetworkFirewallClient.php',
    'Aws\\NetworkFlowMonitor\\Exception\\NetworkFlowMonitorException' => $vendorDir . '/aws/aws-sdk-php/src/NetworkFlowMonitor/Exception/NetworkFlowMonitorException.php',
    'Aws\\NetworkFlowMonitor\\NetworkFlowMonitorClient' => $vendorDir . '/aws/aws-sdk-php/src/NetworkFlowMonitor/NetworkFlowMonitorClient.php',
    'Aws\\NetworkManager\\Exception\\NetworkManagerException' => $vendorDir . '/aws/aws-sdk-php/src/NetworkManager/Exception/NetworkManagerException.php',
    'Aws\\NetworkManager\\NetworkManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/NetworkManager/NetworkManagerClient.php',
    'Aws\\NetworkMonitor\\Exception\\NetworkMonitorException' => $vendorDir . '/aws/aws-sdk-php/src/NetworkMonitor/Exception/NetworkMonitorException.php',
    'Aws\\NetworkMonitor\\NetworkMonitorClient' => $vendorDir . '/aws/aws-sdk-php/src/NetworkMonitor/NetworkMonitorClient.php',
    'Aws\\NotificationsContacts\\Exception\\NotificationsContactsException' => $vendorDir . '/aws/aws-sdk-php/src/NotificationsContacts/Exception/NotificationsContactsException.php',
    'Aws\\NotificationsContacts\\NotificationsContactsClient' => $vendorDir . '/aws/aws-sdk-php/src/NotificationsContacts/NotificationsContactsClient.php',
    'Aws\\Notifications\\Exception\\NotificationsException' => $vendorDir . '/aws/aws-sdk-php/src/Notifications/Exception/NotificationsException.php',
    'Aws\\Notifications\\NotificationsClient' => $vendorDir . '/aws/aws-sdk-php/src/Notifications/NotificationsClient.php',
    'Aws\\OAM\\Exception\\OAMException' => $vendorDir . '/aws/aws-sdk-php/src/OAM/Exception/OAMException.php',
    'Aws\\OAM\\OAMClient' => $vendorDir . '/aws/aws-sdk-php/src/OAM/OAMClient.php',
    'Aws\\OSIS\\Exception\\OSISException' => $vendorDir . '/aws/aws-sdk-php/src/OSIS/Exception/OSISException.php',
    'Aws\\OSIS\\OSISClient' => $vendorDir . '/aws/aws-sdk-php/src/OSIS/OSISClient.php',
    'Aws\\ObservabilityAdmin\\Exception\\ObservabilityAdminException' => $vendorDir . '/aws/aws-sdk-php/src/ObservabilityAdmin/Exception/ObservabilityAdminException.php',
    'Aws\\ObservabilityAdmin\\ObservabilityAdminClient' => $vendorDir . '/aws/aws-sdk-php/src/ObservabilityAdmin/ObservabilityAdminClient.php',
    'Aws\\Odb\\Exception\\OdbException' => $vendorDir . '/aws/aws-sdk-php/src/Odb/Exception/OdbException.php',
    'Aws\\Odb\\OdbClient' => $vendorDir . '/aws/aws-sdk-php/src/Odb/OdbClient.php',
    'Aws\\Omics\\Exception\\OmicsException' => $vendorDir . '/aws/aws-sdk-php/src/Omics/Exception/OmicsException.php',
    'Aws\\Omics\\OmicsClient' => $vendorDir . '/aws/aws-sdk-php/src/Omics/OmicsClient.php',
    'Aws\\OpenSearchServerless\\Exception\\OpenSearchServerlessException' => $vendorDir . '/aws/aws-sdk-php/src/OpenSearchServerless/Exception/OpenSearchServerlessException.php',
    'Aws\\OpenSearchServerless\\OpenSearchServerlessClient' => $vendorDir . '/aws/aws-sdk-php/src/OpenSearchServerless/OpenSearchServerlessClient.php',
    'Aws\\OpenSearchService\\Exception\\OpenSearchServiceException' => $vendorDir . '/aws/aws-sdk-php/src/OpenSearchService/Exception/OpenSearchServiceException.php',
    'Aws\\OpenSearchService\\OpenSearchServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/OpenSearchService/OpenSearchServiceClient.php',
    'Aws\\OpsWorksCM\\Exception\\OpsWorksCMException' => $vendorDir . '/aws/aws-sdk-php/src/OpsWorksCM/Exception/OpsWorksCMException.php',
    'Aws\\OpsWorksCM\\OpsWorksCMClient' => $vendorDir . '/aws/aws-sdk-php/src/OpsWorksCM/OpsWorksCMClient.php',
    'Aws\\OpsWorks\\Exception\\OpsWorksException' => $vendorDir . '/aws/aws-sdk-php/src/OpsWorks/Exception/OpsWorksException.php',
    'Aws\\OpsWorks\\OpsWorksClient' => $vendorDir . '/aws/aws-sdk-php/src/OpsWorks/OpsWorksClient.php',
    'Aws\\Organizations\\Exception\\OrganizationsException' => $vendorDir . '/aws/aws-sdk-php/src/Organizations/Exception/OrganizationsException.php',
    'Aws\\Organizations\\OrganizationsClient' => $vendorDir . '/aws/aws-sdk-php/src/Organizations/OrganizationsClient.php',
    'Aws\\Outposts\\Exception\\OutpostsException' => $vendorDir . '/aws/aws-sdk-php/src/Outposts/Exception/OutpostsException.php',
    'Aws\\Outposts\\OutpostsClient' => $vendorDir . '/aws/aws-sdk-php/src/Outposts/OutpostsClient.php',
    'Aws\\PCS\\Exception\\PCSException' => $vendorDir . '/aws/aws-sdk-php/src/PCS/Exception/PCSException.php',
    'Aws\\PCS\\PCSClient' => $vendorDir . '/aws/aws-sdk-php/src/PCS/PCSClient.php',
    'Aws\\PI\\Exception\\PIException' => $vendorDir . '/aws/aws-sdk-php/src/PI/Exception/PIException.php',
    'Aws\\PI\\PIClient' => $vendorDir . '/aws/aws-sdk-php/src/PI/PIClient.php',
    'Aws\\Panorama\\Exception\\PanoramaException' => $vendorDir . '/aws/aws-sdk-php/src/Panorama/Exception/PanoramaException.php',
    'Aws\\Panorama\\PanoramaClient' => $vendorDir . '/aws/aws-sdk-php/src/Panorama/PanoramaClient.php',
    'Aws\\PartnerCentralSelling\\Exception\\PartnerCentralSellingException' => $vendorDir . '/aws/aws-sdk-php/src/PartnerCentralSelling/Exception/PartnerCentralSellingException.php',
    'Aws\\PartnerCentralSelling\\PartnerCentralSellingClient' => $vendorDir . '/aws/aws-sdk-php/src/PartnerCentralSelling/PartnerCentralSellingClient.php',
    'Aws\\PaymentCryptographyData\\Exception\\PaymentCryptographyDataException' => $vendorDir . '/aws/aws-sdk-php/src/PaymentCryptographyData/Exception/PaymentCryptographyDataException.php',
    'Aws\\PaymentCryptographyData\\PaymentCryptographyDataClient' => $vendorDir . '/aws/aws-sdk-php/src/PaymentCryptographyData/PaymentCryptographyDataClient.php',
    'Aws\\PaymentCryptography\\Exception\\PaymentCryptographyException' => $vendorDir . '/aws/aws-sdk-php/src/PaymentCryptography/Exception/PaymentCryptographyException.php',
    'Aws\\PaymentCryptography\\PaymentCryptographyClient' => $vendorDir . '/aws/aws-sdk-php/src/PaymentCryptography/PaymentCryptographyClient.php',
    'Aws\\PcaConnectorAd\\Exception\\PcaConnectorAdException' => $vendorDir . '/aws/aws-sdk-php/src/PcaConnectorAd/Exception/PcaConnectorAdException.php',
    'Aws\\PcaConnectorAd\\PcaConnectorAdClient' => $vendorDir . '/aws/aws-sdk-php/src/PcaConnectorAd/PcaConnectorAdClient.php',
    'Aws\\PcaConnectorScep\\Exception\\PcaConnectorScepException' => $vendorDir . '/aws/aws-sdk-php/src/PcaConnectorScep/Exception/PcaConnectorScepException.php',
    'Aws\\PcaConnectorScep\\PcaConnectorScepClient' => $vendorDir . '/aws/aws-sdk-php/src/PcaConnectorScep/PcaConnectorScepClient.php',
    'Aws\\PersonalizeEvents\\Exception\\PersonalizeEventsException' => $vendorDir . '/aws/aws-sdk-php/src/PersonalizeEvents/Exception/PersonalizeEventsException.php',
    'Aws\\PersonalizeEvents\\PersonalizeEventsClient' => $vendorDir . '/aws/aws-sdk-php/src/PersonalizeEvents/PersonalizeEventsClient.php',
    'Aws\\PersonalizeRuntime\\Exception\\PersonalizeRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/PersonalizeRuntime/Exception/PersonalizeRuntimeException.php',
    'Aws\\PersonalizeRuntime\\PersonalizeRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/PersonalizeRuntime/PersonalizeRuntimeClient.php',
    'Aws\\Personalize\\Exception\\PersonalizeException' => $vendorDir . '/aws/aws-sdk-php/src/Personalize/Exception/PersonalizeException.php',
    'Aws\\Personalize\\PersonalizeClient' => $vendorDir . '/aws/aws-sdk-php/src/Personalize/PersonalizeClient.php',
    'Aws\\PhpHash' => $vendorDir . '/aws/aws-sdk-php/src/PhpHash.php',
    'Aws\\PinpointEmail\\Exception\\PinpointEmailException' => $vendorDir . '/aws/aws-sdk-php/src/PinpointEmail/Exception/PinpointEmailException.php',
    'Aws\\PinpointEmail\\PinpointEmailClient' => $vendorDir . '/aws/aws-sdk-php/src/PinpointEmail/PinpointEmailClient.php',
    'Aws\\PinpointSMSVoiceV2\\Exception\\PinpointSMSVoiceV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/PinpointSMSVoiceV2/Exception/PinpointSMSVoiceV2Exception.php',
    'Aws\\PinpointSMSVoiceV2\\PinpointSMSVoiceV2Client' => $vendorDir . '/aws/aws-sdk-php/src/PinpointSMSVoiceV2/PinpointSMSVoiceV2Client.php',
    'Aws\\PinpointSMSVoice\\Exception\\PinpointSMSVoiceException' => $vendorDir . '/aws/aws-sdk-php/src/PinpointSMSVoice/Exception/PinpointSMSVoiceException.php',
    'Aws\\PinpointSMSVoice\\PinpointSMSVoiceClient' => $vendorDir . '/aws/aws-sdk-php/src/PinpointSMSVoice/PinpointSMSVoiceClient.php',
    'Aws\\Pinpoint\\Exception\\PinpointException' => $vendorDir . '/aws/aws-sdk-php/src/Pinpoint/Exception/PinpointException.php',
    'Aws\\Pinpoint\\PinpointClient' => $vendorDir . '/aws/aws-sdk-php/src/Pinpoint/PinpointClient.php',
    'Aws\\Pipes\\Exception\\PipesException' => $vendorDir . '/aws/aws-sdk-php/src/Pipes/Exception/PipesException.php',
    'Aws\\Pipes\\PipesClient' => $vendorDir . '/aws/aws-sdk-php/src/Pipes/PipesClient.php',
    'Aws\\Polly\\Exception\\PollyException' => $vendorDir . '/aws/aws-sdk-php/src/Polly/Exception/PollyException.php',
    'Aws\\Polly\\PollyClient' => $vendorDir . '/aws/aws-sdk-php/src/Polly/PollyClient.php',
    'Aws\\PresignUrlMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/PresignUrlMiddleware.php',
    'Aws\\Pricing\\Exception\\PricingException' => $vendorDir . '/aws/aws-sdk-php/src/Pricing/Exception/PricingException.php',
    'Aws\\Pricing\\PricingClient' => $vendorDir . '/aws/aws-sdk-php/src/Pricing/PricingClient.php',
    'Aws\\PrometheusService\\Exception\\PrometheusServiceException' => $vendorDir . '/aws/aws-sdk-php/src/PrometheusService/Exception/PrometheusServiceException.php',
    'Aws\\PrometheusService\\PrometheusServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/PrometheusService/PrometheusServiceClient.php',
    'Aws\\Proton\\Exception\\ProtonException' => $vendorDir . '/aws/aws-sdk-php/src/Proton/Exception/ProtonException.php',
    'Aws\\Proton\\ProtonClient' => $vendorDir . '/aws/aws-sdk-php/src/Proton/ProtonClient.php',
    'Aws\\Psr16CacheAdapter' => $vendorDir . '/aws/aws-sdk-php/src/Psr16CacheAdapter.php',
    'Aws\\PsrCacheAdapter' => $vendorDir . '/aws/aws-sdk-php/src/PsrCacheAdapter.php',
    'Aws\\QApps\\Exception\\QAppsException' => $vendorDir . '/aws/aws-sdk-php/src/QApps/Exception/QAppsException.php',
    'Aws\\QApps\\QAppsClient' => $vendorDir . '/aws/aws-sdk-php/src/QApps/QAppsClient.php',
    'Aws\\QBusiness\\Exception\\QBusinessException' => $vendorDir . '/aws/aws-sdk-php/src/QBusiness/Exception/QBusinessException.php',
    'Aws\\QBusiness\\QBusinessClient' => $vendorDir . '/aws/aws-sdk-php/src/QBusiness/QBusinessClient.php',
    'Aws\\QConnect\\Exception\\QConnectException' => $vendorDir . '/aws/aws-sdk-php/src/QConnect/Exception/QConnectException.php',
    'Aws\\QConnect\\QConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/QConnect/QConnectClient.php',
    'Aws\\QLDBSession\\Exception\\QLDBSessionException' => $vendorDir . '/aws/aws-sdk-php/src/QLDBSession/Exception/QLDBSessionException.php',
    'Aws\\QLDBSession\\QLDBSessionClient' => $vendorDir . '/aws/aws-sdk-php/src/QLDBSession/QLDBSessionClient.php',
    'Aws\\QLDB\\Exception\\QLDBException' => $vendorDir . '/aws/aws-sdk-php/src/QLDB/Exception/QLDBException.php',
    'Aws\\QLDB\\QLDBClient' => $vendorDir . '/aws/aws-sdk-php/src/QLDB/QLDBClient.php',
    'Aws\\QueryCompatibleInputMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/QueryCompatibleInputMiddleware.php',
    'Aws\\QuickSight\\Exception\\QuickSightException' => $vendorDir . '/aws/aws-sdk-php/src/QuickSight/Exception/QuickSightException.php',
    'Aws\\QuickSight\\QuickSightClient' => $vendorDir . '/aws/aws-sdk-php/src/QuickSight/QuickSightClient.php',
    'Aws\\RAM\\Exception\\RAMException' => $vendorDir . '/aws/aws-sdk-php/src/RAM/Exception/RAMException.php',
    'Aws\\RAM\\RAMClient' => $vendorDir . '/aws/aws-sdk-php/src/RAM/RAMClient.php',
    'Aws\\RDSDataService\\Exception\\RDSDataServiceException' => $vendorDir . '/aws/aws-sdk-php/src/RDSDataService/Exception/RDSDataServiceException.php',
    'Aws\\RDSDataService\\RDSDataServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/RDSDataService/RDSDataServiceClient.php',
    'Aws\\Rds\\AuthTokenGenerator' => $vendorDir . '/aws/aws-sdk-php/src/Rds/AuthTokenGenerator.php',
    'Aws\\Rds\\Exception\\RdsException' => $vendorDir . '/aws/aws-sdk-php/src/Rds/Exception/RdsException.php',
    'Aws\\Rds\\RdsClient' => $vendorDir . '/aws/aws-sdk-php/src/Rds/RdsClient.php',
    'Aws\\RecycleBin\\Exception\\RecycleBinException' => $vendorDir . '/aws/aws-sdk-php/src/RecycleBin/Exception/RecycleBinException.php',
    'Aws\\RecycleBin\\RecycleBinClient' => $vendorDir . '/aws/aws-sdk-php/src/RecycleBin/RecycleBinClient.php',
    'Aws\\RedshiftDataAPIService\\Exception\\RedshiftDataAPIServiceException' => $vendorDir . '/aws/aws-sdk-php/src/RedshiftDataAPIService/Exception/RedshiftDataAPIServiceException.php',
    'Aws\\RedshiftDataAPIService\\RedshiftDataAPIServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/RedshiftDataAPIService/RedshiftDataAPIServiceClient.php',
    'Aws\\RedshiftServerless\\Exception\\RedshiftServerlessException' => $vendorDir . '/aws/aws-sdk-php/src/RedshiftServerless/Exception/RedshiftServerlessException.php',
    'Aws\\RedshiftServerless\\RedshiftServerlessClient' => $vendorDir . '/aws/aws-sdk-php/src/RedshiftServerless/RedshiftServerlessClient.php',
    'Aws\\Redshift\\Exception\\RedshiftException' => $vendorDir . '/aws/aws-sdk-php/src/Redshift/Exception/RedshiftException.php',
    'Aws\\Redshift\\RedshiftClient' => $vendorDir . '/aws/aws-sdk-php/src/Redshift/RedshiftClient.php',
    'Aws\\Rekognition\\Exception\\RekognitionException' => $vendorDir . '/aws/aws-sdk-php/src/Rekognition/Exception/RekognitionException.php',
    'Aws\\Rekognition\\RekognitionClient' => $vendorDir . '/aws/aws-sdk-php/src/Rekognition/RekognitionClient.php',
    'Aws\\Repostspace\\Exception\\RepostspaceException' => $vendorDir . '/aws/aws-sdk-php/src/Repostspace/Exception/RepostspaceException.php',
    'Aws\\Repostspace\\RepostspaceClient' => $vendorDir . '/aws/aws-sdk-php/src/Repostspace/RepostspaceClient.php',
    'Aws\\RequestCompressionMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/RequestCompressionMiddleware.php',
    'Aws\\ResilienceHub\\Exception\\ResilienceHubException' => $vendorDir . '/aws/aws-sdk-php/src/ResilienceHub/Exception/ResilienceHubException.php',
    'Aws\\ResilienceHub\\ResilienceHubClient' => $vendorDir . '/aws/aws-sdk-php/src/ResilienceHub/ResilienceHubClient.php',
    'Aws\\ResourceExplorer2\\Exception\\ResourceExplorer2Exception' => $vendorDir . '/aws/aws-sdk-php/src/ResourceExplorer2/Exception/ResourceExplorer2Exception.php',
    'Aws\\ResourceExplorer2\\ResourceExplorer2Client' => $vendorDir . '/aws/aws-sdk-php/src/ResourceExplorer2/ResourceExplorer2Client.php',
    'Aws\\ResourceGroupsTaggingAPI\\Exception\\ResourceGroupsTaggingAPIException' => $vendorDir . '/aws/aws-sdk-php/src/ResourceGroupsTaggingAPI/Exception/ResourceGroupsTaggingAPIException.php',
    'Aws\\ResourceGroupsTaggingAPI\\ResourceGroupsTaggingAPIClient' => $vendorDir . '/aws/aws-sdk-php/src/ResourceGroupsTaggingAPI/ResourceGroupsTaggingAPIClient.php',
    'Aws\\ResourceGroups\\Exception\\ResourceGroupsException' => $vendorDir . '/aws/aws-sdk-php/src/ResourceGroups/Exception/ResourceGroupsException.php',
    'Aws\\ResourceGroups\\ResourceGroupsClient' => $vendorDir . '/aws/aws-sdk-php/src/ResourceGroups/ResourceGroupsClient.php',
    'Aws\\ResponseContainerInterface' => $vendorDir . '/aws/aws-sdk-php/src/ResponseContainerInterface.php',
    'Aws\\Result' => $vendorDir . '/aws/aws-sdk-php/src/Result.php',
    'Aws\\ResultInterface' => $vendorDir . '/aws/aws-sdk-php/src/ResultInterface.php',
    'Aws\\ResultPaginator' => $vendorDir . '/aws/aws-sdk-php/src/ResultPaginator.php',
    'Aws\\RetryMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/RetryMiddleware.php',
    'Aws\\RetryMiddlewareV2' => $vendorDir . '/aws/aws-sdk-php/src/RetryMiddlewareV2.php',
    'Aws\\Retry\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/Retry/Configuration.php',
    'Aws\\Retry\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/Retry/ConfigurationInterface.php',
    'Aws\\Retry\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/Retry/ConfigurationProvider.php',
    'Aws\\Retry\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/Retry/Exception/ConfigurationException.php',
    'Aws\\Retry\\QuotaManager' => $vendorDir . '/aws/aws-sdk-php/src/Retry/QuotaManager.php',
    'Aws\\Retry\\RateLimiter' => $vendorDir . '/aws/aws-sdk-php/src/Retry/RateLimiter.php',
    'Aws\\Retry\\RetryHelperTrait' => $vendorDir . '/aws/aws-sdk-php/src/Retry/RetryHelperTrait.php',
    'Aws\\RoboMaker\\Exception\\RoboMakerException' => $vendorDir . '/aws/aws-sdk-php/src/RoboMaker/Exception/RoboMakerException.php',
    'Aws\\RoboMaker\\RoboMakerClient' => $vendorDir . '/aws/aws-sdk-php/src/RoboMaker/RoboMakerClient.php',
    'Aws\\RolesAnywhere\\Exception\\RolesAnywhereException' => $vendorDir . '/aws/aws-sdk-php/src/RolesAnywhere/Exception/RolesAnywhereException.php',
    'Aws\\RolesAnywhere\\RolesAnywhereClient' => $vendorDir . '/aws/aws-sdk-php/src/RolesAnywhere/RolesAnywhereClient.php',
    'Aws\\Route53Domains\\Exception\\Route53DomainsException' => $vendorDir . '/aws/aws-sdk-php/src/Route53Domains/Exception/Route53DomainsException.php',
    'Aws\\Route53Domains\\Route53DomainsClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53Domains/Route53DomainsClient.php',
    'Aws\\Route53Profiles\\Exception\\Route53ProfilesException' => $vendorDir . '/aws/aws-sdk-php/src/Route53Profiles/Exception/Route53ProfilesException.php',
    'Aws\\Route53Profiles\\Route53ProfilesClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53Profiles/Route53ProfilesClient.php',
    'Aws\\Route53RecoveryCluster\\Exception\\Route53RecoveryClusterException' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryCluster/Exception/Route53RecoveryClusterException.php',
    'Aws\\Route53RecoveryCluster\\Route53RecoveryClusterClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryCluster/Route53RecoveryClusterClient.php',
    'Aws\\Route53RecoveryControlConfig\\Exception\\Route53RecoveryControlConfigException' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryControlConfig/Exception/Route53RecoveryControlConfigException.php',
    'Aws\\Route53RecoveryControlConfig\\Route53RecoveryControlConfigClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryControlConfig/Route53RecoveryControlConfigClient.php',
    'Aws\\Route53RecoveryReadiness\\Exception\\Route53RecoveryReadinessException' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryReadiness/Exception/Route53RecoveryReadinessException.php',
    'Aws\\Route53RecoveryReadiness\\Route53RecoveryReadinessClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53RecoveryReadiness/Route53RecoveryReadinessClient.php',
    'Aws\\Route53Resolver\\Exception\\Route53ResolverException' => $vendorDir . '/aws/aws-sdk-php/src/Route53Resolver/Exception/Route53ResolverException.php',
    'Aws\\Route53Resolver\\Route53ResolverClient' => $vendorDir . '/aws/aws-sdk-php/src/Route53Resolver/Route53ResolverClient.php',
    'Aws\\Route53\\Exception\\Route53Exception' => $vendorDir . '/aws/aws-sdk-php/src/Route53/Exception/Route53Exception.php',
    'Aws\\Route53\\Route53Client' => $vendorDir . '/aws/aws-sdk-php/src/Route53/Route53Client.php',
    'Aws\\S3Control\\EndpointArnMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3Control/EndpointArnMiddleware.php',
    'Aws\\S3Control\\Exception\\S3ControlException' => $vendorDir . '/aws/aws-sdk-php/src/S3Control/Exception/S3ControlException.php',
    'Aws\\S3Control\\S3ControlClient' => $vendorDir . '/aws/aws-sdk-php/src/S3Control/S3ControlClient.php',
    'Aws\\S3Outposts\\Exception\\S3OutpostsException' => $vendorDir . '/aws/aws-sdk-php/src/S3Outposts/Exception/S3OutpostsException.php',
    'Aws\\S3Outposts\\S3OutpostsClient' => $vendorDir . '/aws/aws-sdk-php/src/S3Outposts/S3OutpostsClient.php',
    'Aws\\S3Tables\\Exception\\S3TablesException' => $vendorDir . '/aws/aws-sdk-php/src/S3Tables/Exception/S3TablesException.php',
    'Aws\\S3Tables\\S3TablesClient' => $vendorDir . '/aws/aws-sdk-php/src/S3Tables/S3TablesClient.php',
    'Aws\\S3Vectors\\Exception\\S3VectorsException' => $vendorDir . '/aws/aws-sdk-php/src/S3Vectors/Exception/S3VectorsException.php',
    'Aws\\S3Vectors\\S3VectorsClient' => $vendorDir . '/aws/aws-sdk-php/src/S3Vectors/S3VectorsClient.php',
    'Aws\\S3\\AmbiguousSuccessParser' => $vendorDir . '/aws/aws-sdk-php/src/S3/AmbiguousSuccessParser.php',
    'Aws\\S3\\ApplyChecksumMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/ApplyChecksumMiddleware.php',
    'Aws\\S3\\BatchDelete' => $vendorDir . '/aws/aws-sdk-php/src/S3/BatchDelete.php',
    'Aws\\S3\\BucketEndpointArnMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/BucketEndpointArnMiddleware.php',
    'Aws\\S3\\BucketEndpointMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/BucketEndpointMiddleware.php',
    'Aws\\S3\\CalculatesChecksumTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/CalculatesChecksumTrait.php',
    'Aws\\S3\\Crypto\\CryptoParamsTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/CryptoParamsTrait.php',
    'Aws\\S3\\Crypto\\CryptoParamsTraitV2' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/CryptoParamsTraitV2.php',
    'Aws\\S3\\Crypto\\HeadersMetadataStrategy' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/HeadersMetadataStrategy.php',
    'Aws\\S3\\Crypto\\InstructionFileMetadataStrategy' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/InstructionFileMetadataStrategy.php',
    'Aws\\S3\\Crypto\\S3EncryptionClient' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/S3EncryptionClient.php',
    'Aws\\S3\\Crypto\\S3EncryptionClientV2' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/S3EncryptionClientV2.php',
    'Aws\\S3\\Crypto\\S3EncryptionMultipartUploader' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/S3EncryptionMultipartUploader.php',
    'Aws\\S3\\Crypto\\S3EncryptionMultipartUploaderV2' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/S3EncryptionMultipartUploaderV2.php',
    'Aws\\S3\\Crypto\\UserAgentTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/Crypto/UserAgentTrait.php',
    'Aws\\S3\\EndpointRegionHelperTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/EndpointRegionHelperTrait.php',
    'Aws\\S3\\Exception\\DeleteMultipleObjectsException' => $vendorDir . '/aws/aws-sdk-php/src/S3/Exception/DeleteMultipleObjectsException.php',
    'Aws\\S3\\Exception\\PermanentRedirectException' => $vendorDir . '/aws/aws-sdk-php/src/S3/Exception/PermanentRedirectException.php',
    'Aws\\S3\\Exception\\S3Exception' => $vendorDir . '/aws/aws-sdk-php/src/S3/Exception/S3Exception.php',
    'Aws\\S3\\Exception\\S3MultipartUploadException' => $vendorDir . '/aws/aws-sdk-php/src/S3/Exception/S3MultipartUploadException.php',
    'Aws\\S3\\ExpiresParsingMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/ExpiresParsingMiddleware.php',
    'Aws\\S3\\GetBucketLocationParser' => $vendorDir . '/aws/aws-sdk-php/src/S3/GetBucketLocationParser.php',
    'Aws\\S3\\MultipartCopy' => $vendorDir . '/aws/aws-sdk-php/src/S3/MultipartCopy.php',
    'Aws\\S3\\MultipartUploader' => $vendorDir . '/aws/aws-sdk-php/src/S3/MultipartUploader.php',
    'Aws\\S3\\MultipartUploadingTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/MultipartUploadingTrait.php',
    'Aws\\S3\\ObjectCopier' => $vendorDir . '/aws/aws-sdk-php/src/S3/ObjectCopier.php',
    'Aws\\S3\\ObjectUploader' => $vendorDir . '/aws/aws-sdk-php/src/S3/ObjectUploader.php',
    'Aws\\S3\\Parser\\GetBucketLocationResultMutator' => $vendorDir . '/aws/aws-sdk-php/src/S3/Parser/GetBucketLocationResultMutator.php',
    'Aws\\S3\\Parser\\S3Parser' => $vendorDir . '/aws/aws-sdk-php/src/S3/Parser/S3Parser.php',
    'Aws\\S3\\Parser\\S3ResultMutator' => $vendorDir . '/aws/aws-sdk-php/src/S3/Parser/S3ResultMutator.php',
    'Aws\\S3\\Parser\\ValidateResponseChecksumResultMutator' => $vendorDir . '/aws/aws-sdk-php/src/S3/Parser/ValidateResponseChecksumResultMutator.php',
    'Aws\\S3\\PermanentRedirectMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/PermanentRedirectMiddleware.php',
    'Aws\\S3\\PostObject' => $vendorDir . '/aws/aws-sdk-php/src/S3/PostObject.php',
    'Aws\\S3\\PostObjectV4' => $vendorDir . '/aws/aws-sdk-php/src/S3/PostObjectV4.php',
    'Aws\\S3\\PutObjectUrlMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/PutObjectUrlMiddleware.php',
    'Aws\\S3\\RegionalEndpoint\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/S3/RegionalEndpoint/Configuration.php',
    'Aws\\S3\\RegionalEndpoint\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/S3/RegionalEndpoint/ConfigurationInterface.php',
    'Aws\\S3\\RegionalEndpoint\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/S3/RegionalEndpoint/ConfigurationProvider.php',
    'Aws\\S3\\RegionalEndpoint\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/S3/RegionalEndpoint/Exception/ConfigurationException.php',
    'Aws\\S3\\RetryableMalformedResponseParser' => $vendorDir . '/aws/aws-sdk-php/src/S3/RetryableMalformedResponseParser.php',
    'Aws\\S3\\S3Client' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3Client.php',
    'Aws\\S3\\S3ClientInterface' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3ClientInterface.php',
    'Aws\\S3\\S3ClientTrait' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3ClientTrait.php',
    'Aws\\S3\\S3EndpointMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3EndpointMiddleware.php',
    'Aws\\S3\\S3MultiRegionClient' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3MultiRegionClient.php',
    'Aws\\S3\\S3UriParser' => $vendorDir . '/aws/aws-sdk-php/src/S3/S3UriParser.php',
    'Aws\\S3\\SSECMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/S3/SSECMiddleware.php',
    'Aws\\S3\\StreamWrapper' => $vendorDir . '/aws/aws-sdk-php/src/S3/StreamWrapper.php',
    'Aws\\S3\\Transfer' => $vendorDir . '/aws/aws-sdk-php/src/S3/Transfer.php',
    'Aws\\S3\\UseArnRegion\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/S3/UseArnRegion/Configuration.php',
    'Aws\\S3\\UseArnRegion\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/S3/UseArnRegion/ConfigurationInterface.php',
    'Aws\\S3\\UseArnRegion\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/S3/UseArnRegion/ConfigurationProvider.php',
    'Aws\\S3\\UseArnRegion\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/S3/UseArnRegion/Exception/ConfigurationException.php',
    'Aws\\S3\\ValidateResponseChecksumParser' => $vendorDir . '/aws/aws-sdk-php/src/S3/ValidateResponseChecksumParser.php',
    'Aws\\SSMContacts\\Exception\\SSMContactsException' => $vendorDir . '/aws/aws-sdk-php/src/SSMContacts/Exception/SSMContactsException.php',
    'Aws\\SSMContacts\\SSMContactsClient' => $vendorDir . '/aws/aws-sdk-php/src/SSMContacts/SSMContactsClient.php',
    'Aws\\SSMGuiConnect\\Exception\\SSMGuiConnectException' => $vendorDir . '/aws/aws-sdk-php/src/SSMGuiConnect/Exception/SSMGuiConnectException.php',
    'Aws\\SSMGuiConnect\\SSMGuiConnectClient' => $vendorDir . '/aws/aws-sdk-php/src/SSMGuiConnect/SSMGuiConnectClient.php',
    'Aws\\SSMIncidents\\Exception\\SSMIncidentsException' => $vendorDir . '/aws/aws-sdk-php/src/SSMIncidents/Exception/SSMIncidentsException.php',
    'Aws\\SSMIncidents\\SSMIncidentsClient' => $vendorDir . '/aws/aws-sdk-php/src/SSMIncidents/SSMIncidentsClient.php',
    'Aws\\SSMQuickSetup\\Exception\\SSMQuickSetupException' => $vendorDir . '/aws/aws-sdk-php/src/SSMQuickSetup/Exception/SSMQuickSetupException.php',
    'Aws\\SSMQuickSetup\\SSMQuickSetupClient' => $vendorDir . '/aws/aws-sdk-php/src/SSMQuickSetup/SSMQuickSetupClient.php',
    'Aws\\SSOAdmin\\Exception\\SSOAdminException' => $vendorDir . '/aws/aws-sdk-php/src/SSOAdmin/Exception/SSOAdminException.php',
    'Aws\\SSOAdmin\\SSOAdminClient' => $vendorDir . '/aws/aws-sdk-php/src/SSOAdmin/SSOAdminClient.php',
    'Aws\\SSOOIDC\\Exception\\SSOOIDCException' => $vendorDir . '/aws/aws-sdk-php/src/SSOOIDC/Exception/SSOOIDCException.php',
    'Aws\\SSOOIDC\\SSOOIDCClient' => $vendorDir . '/aws/aws-sdk-php/src/SSOOIDC/SSOOIDCClient.php',
    'Aws\\SSO\\Exception\\SSOException' => $vendorDir . '/aws/aws-sdk-php/src/SSO/Exception/SSOException.php',
    'Aws\\SSO\\SSOClient' => $vendorDir . '/aws/aws-sdk-php/src/SSO/SSOClient.php',
    'Aws\\SageMakerFeatureStoreRuntime\\Exception\\SageMakerFeatureStoreRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerFeatureStoreRuntime/Exception/SageMakerFeatureStoreRuntimeException.php',
    'Aws\\SageMakerFeatureStoreRuntime\\SageMakerFeatureStoreRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerFeatureStoreRuntime/SageMakerFeatureStoreRuntimeClient.php',
    'Aws\\SageMakerGeospatial\\Exception\\SageMakerGeospatialException' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerGeospatial/Exception/SageMakerGeospatialException.php',
    'Aws\\SageMakerGeospatial\\SageMakerGeospatialClient' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerGeospatial/SageMakerGeospatialClient.php',
    'Aws\\SageMakerMetrics\\Exception\\SageMakerMetricsException' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerMetrics/Exception/SageMakerMetricsException.php',
    'Aws\\SageMakerMetrics\\SageMakerMetricsClient' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerMetrics/SageMakerMetricsClient.php',
    'Aws\\SageMakerRuntime\\Exception\\SageMakerRuntimeException' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerRuntime/Exception/SageMakerRuntimeException.php',
    'Aws\\SageMakerRuntime\\SageMakerRuntimeClient' => $vendorDir . '/aws/aws-sdk-php/src/SageMakerRuntime/SageMakerRuntimeClient.php',
    'Aws\\SageMaker\\Exception\\SageMakerException' => $vendorDir . '/aws/aws-sdk-php/src/SageMaker/Exception/SageMakerException.php',
    'Aws\\SageMaker\\SageMakerClient' => $vendorDir . '/aws/aws-sdk-php/src/SageMaker/SageMakerClient.php',
    'Aws\\SagemakerEdgeManager\\Exception\\SagemakerEdgeManagerException' => $vendorDir . '/aws/aws-sdk-php/src/SagemakerEdgeManager/Exception/SagemakerEdgeManagerException.php',
    'Aws\\SagemakerEdgeManager\\SagemakerEdgeManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/SagemakerEdgeManager/SagemakerEdgeManagerClient.php',
    'Aws\\SavingsPlans\\Exception\\SavingsPlansException' => $vendorDir . '/aws/aws-sdk-php/src/SavingsPlans/Exception/SavingsPlansException.php',
    'Aws\\SavingsPlans\\SavingsPlansClient' => $vendorDir . '/aws/aws-sdk-php/src/SavingsPlans/SavingsPlansClient.php',
    'Aws\\Scheduler\\Exception\\SchedulerException' => $vendorDir . '/aws/aws-sdk-php/src/Scheduler/Exception/SchedulerException.php',
    'Aws\\Scheduler\\SchedulerClient' => $vendorDir . '/aws/aws-sdk-php/src/Scheduler/SchedulerClient.php',
    'Aws\\Schemas\\Exception\\SchemasException' => $vendorDir . '/aws/aws-sdk-php/src/Schemas/Exception/SchemasException.php',
    'Aws\\Schemas\\SchemasClient' => $vendorDir . '/aws/aws-sdk-php/src/Schemas/SchemasClient.php',
    'Aws\\Script\\Composer\\Composer' => $vendorDir . '/aws/aws-sdk-php/src/Script/Composer/Composer.php',
    'Aws\\Sdk' => $vendorDir . '/aws/aws-sdk-php/src/Sdk.php',
    'Aws\\SecretsManager\\Exception\\SecretsManagerException' => $vendorDir . '/aws/aws-sdk-php/src/SecretsManager/Exception/SecretsManagerException.php',
    'Aws\\SecretsManager\\SecretsManagerClient' => $vendorDir . '/aws/aws-sdk-php/src/SecretsManager/SecretsManagerClient.php',
    'Aws\\SecurityHub\\Exception\\SecurityHubException' => $vendorDir . '/aws/aws-sdk-php/src/SecurityHub/Exception/SecurityHubException.php',
    'Aws\\SecurityHub\\SecurityHubClient' => $vendorDir . '/aws/aws-sdk-php/src/SecurityHub/SecurityHubClient.php',
    'Aws\\SecurityIR\\Exception\\SecurityIRException' => $vendorDir . '/aws/aws-sdk-php/src/SecurityIR/Exception/SecurityIRException.php',
    'Aws\\SecurityIR\\SecurityIRClient' => $vendorDir . '/aws/aws-sdk-php/src/SecurityIR/SecurityIRClient.php',
    'Aws\\SecurityLake\\Exception\\SecurityLakeException' => $vendorDir . '/aws/aws-sdk-php/src/SecurityLake/Exception/SecurityLakeException.php',
    'Aws\\SecurityLake\\SecurityLakeClient' => $vendorDir . '/aws/aws-sdk-php/src/SecurityLake/SecurityLakeClient.php',
    'Aws\\ServerlessApplicationRepository\\Exception\\ServerlessApplicationRepositoryException' => $vendorDir . '/aws/aws-sdk-php/src/ServerlessApplicationRepository/Exception/ServerlessApplicationRepositoryException.php',
    'Aws\\ServerlessApplicationRepository\\ServerlessApplicationRepositoryClient' => $vendorDir . '/aws/aws-sdk-php/src/ServerlessApplicationRepository/ServerlessApplicationRepositoryClient.php',
    'Aws\\ServiceCatalog\\Exception\\ServiceCatalogException' => $vendorDir . '/aws/aws-sdk-php/src/ServiceCatalog/Exception/ServiceCatalogException.php',
    'Aws\\ServiceCatalog\\ServiceCatalogClient' => $vendorDir . '/aws/aws-sdk-php/src/ServiceCatalog/ServiceCatalogClient.php',
    'Aws\\ServiceDiscovery\\Exception\\ServiceDiscoveryException' => $vendorDir . '/aws/aws-sdk-php/src/ServiceDiscovery/Exception/ServiceDiscoveryException.php',
    'Aws\\ServiceDiscovery\\ServiceDiscoveryClient' => $vendorDir . '/aws/aws-sdk-php/src/ServiceDiscovery/ServiceDiscoveryClient.php',
    'Aws\\ServiceQuotas\\Exception\\ServiceQuotasException' => $vendorDir . '/aws/aws-sdk-php/src/ServiceQuotas/Exception/ServiceQuotasException.php',
    'Aws\\ServiceQuotas\\ServiceQuotasClient' => $vendorDir . '/aws/aws-sdk-php/src/ServiceQuotas/ServiceQuotasClient.php',
    'Aws\\SesV2\\Exception\\SesV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/SesV2/Exception/SesV2Exception.php',
    'Aws\\SesV2\\SesV2Client' => $vendorDir . '/aws/aws-sdk-php/src/SesV2/SesV2Client.php',
    'Aws\\Ses\\Exception\\SesException' => $vendorDir . '/aws/aws-sdk-php/src/Ses/Exception/SesException.php',
    'Aws\\Ses\\SesClient' => $vendorDir . '/aws/aws-sdk-php/src/Ses/SesClient.php',
    'Aws\\Sfn\\Exception\\SfnException' => $vendorDir . '/aws/aws-sdk-php/src/Sfn/Exception/SfnException.php',
    'Aws\\Sfn\\SfnClient' => $vendorDir . '/aws/aws-sdk-php/src/Sfn/SfnClient.php',
    'Aws\\Shield\\Exception\\ShieldException' => $vendorDir . '/aws/aws-sdk-php/src/Shield/Exception/ShieldException.php',
    'Aws\\Shield\\ShieldClient' => $vendorDir . '/aws/aws-sdk-php/src/Shield/ShieldClient.php',
    'Aws\\Signature\\AnonymousSignature' => $vendorDir . '/aws/aws-sdk-php/src/Signature/AnonymousSignature.php',
    'Aws\\Signature\\S3ExpressSignature' => $vendorDir . '/aws/aws-sdk-php/src/Signature/S3ExpressSignature.php',
    'Aws\\Signature\\S3SignatureV4' => $vendorDir . '/aws/aws-sdk-php/src/Signature/S3SignatureV4.php',
    'Aws\\Signature\\SignatureInterface' => $vendorDir . '/aws/aws-sdk-php/src/Signature/SignatureInterface.php',
    'Aws\\Signature\\SignatureProvider' => $vendorDir . '/aws/aws-sdk-php/src/Signature/SignatureProvider.php',
    'Aws\\Signature\\SignatureTrait' => $vendorDir . '/aws/aws-sdk-php/src/Signature/SignatureTrait.php',
    'Aws\\Signature\\SignatureV4' => $vendorDir . '/aws/aws-sdk-php/src/Signature/SignatureV4.php',
    'Aws\\SimSpaceWeaver\\Exception\\SimSpaceWeaverException' => $vendorDir . '/aws/aws-sdk-php/src/SimSpaceWeaver/Exception/SimSpaceWeaverException.php',
    'Aws\\SimSpaceWeaver\\SimSpaceWeaverClient' => $vendorDir . '/aws/aws-sdk-php/src/SimSpaceWeaver/SimSpaceWeaverClient.php',
    'Aws\\Sms\\Exception\\SmsException' => $vendorDir . '/aws/aws-sdk-php/src/Sms/Exception/SmsException.php',
    'Aws\\Sms\\SmsClient' => $vendorDir . '/aws/aws-sdk-php/src/Sms/SmsClient.php',
    'Aws\\SnowBall\\Exception\\SnowBallException' => $vendorDir . '/aws/aws-sdk-php/src/SnowBall/Exception/SnowBallException.php',
    'Aws\\SnowBall\\SnowBallClient' => $vendorDir . '/aws/aws-sdk-php/src/SnowBall/SnowBallClient.php',
    'Aws\\SnowDeviceManagement\\Exception\\SnowDeviceManagementException' => $vendorDir . '/aws/aws-sdk-php/src/SnowDeviceManagement/Exception/SnowDeviceManagementException.php',
    'Aws\\SnowDeviceManagement\\SnowDeviceManagementClient' => $vendorDir . '/aws/aws-sdk-php/src/SnowDeviceManagement/SnowDeviceManagementClient.php',
    'Aws\\Sns\\Exception\\SnsException' => $vendorDir . '/aws/aws-sdk-php/src/Sns/Exception/SnsException.php',
    'Aws\\Sns\\SnsClient' => $vendorDir . '/aws/aws-sdk-php/src/Sns/SnsClient.php',
    'Aws\\SocialMessaging\\Exception\\SocialMessagingException' => $vendorDir . '/aws/aws-sdk-php/src/SocialMessaging/Exception/SocialMessagingException.php',
    'Aws\\SocialMessaging\\SocialMessagingClient' => $vendorDir . '/aws/aws-sdk-php/src/SocialMessaging/SocialMessagingClient.php',
    'Aws\\Sqs\\Exception\\SqsException' => $vendorDir . '/aws/aws-sdk-php/src/Sqs/Exception/SqsException.php',
    'Aws\\Sqs\\SqsClient' => $vendorDir . '/aws/aws-sdk-php/src/Sqs/SqsClient.php',
    'Aws\\SsmSap\\Exception\\SsmSapException' => $vendorDir . '/aws/aws-sdk-php/src/SsmSap/Exception/SsmSapException.php',
    'Aws\\SsmSap\\SsmSapClient' => $vendorDir . '/aws/aws-sdk-php/src/SsmSap/SsmSapClient.php',
    'Aws\\Ssm\\Exception\\SsmException' => $vendorDir . '/aws/aws-sdk-php/src/Ssm/Exception/SsmException.php',
    'Aws\\Ssm\\SsmClient' => $vendorDir . '/aws/aws-sdk-php/src/Ssm/SsmClient.php',
    'Aws\\StorageGateway\\Exception\\StorageGatewayException' => $vendorDir . '/aws/aws-sdk-php/src/StorageGateway/Exception/StorageGatewayException.php',
    'Aws\\StorageGateway\\StorageGatewayClient' => $vendorDir . '/aws/aws-sdk-php/src/StorageGateway/StorageGatewayClient.php',
    'Aws\\StreamRequestPayloadMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/StreamRequestPayloadMiddleware.php',
    'Aws\\Sts\\Exception\\StsException' => $vendorDir . '/aws/aws-sdk-php/src/Sts/Exception/StsException.php',
    'Aws\\Sts\\RegionalEndpoints\\Configuration' => $vendorDir . '/aws/aws-sdk-php/src/Sts/RegionalEndpoints/Configuration.php',
    'Aws\\Sts\\RegionalEndpoints\\ConfigurationInterface' => $vendorDir . '/aws/aws-sdk-php/src/Sts/RegionalEndpoints/ConfigurationInterface.php',
    'Aws\\Sts\\RegionalEndpoints\\ConfigurationProvider' => $vendorDir . '/aws/aws-sdk-php/src/Sts/RegionalEndpoints/ConfigurationProvider.php',
    'Aws\\Sts\\RegionalEndpoints\\Exception\\ConfigurationException' => $vendorDir . '/aws/aws-sdk-php/src/Sts/RegionalEndpoints/Exception/ConfigurationException.php',
    'Aws\\Sts\\StsClient' => $vendorDir . '/aws/aws-sdk-php/src/Sts/StsClient.php',
    'Aws\\SupplyChain\\Exception\\SupplyChainException' => $vendorDir . '/aws/aws-sdk-php/src/SupplyChain/Exception/SupplyChainException.php',
    'Aws\\SupplyChain\\SupplyChainClient' => $vendorDir . '/aws/aws-sdk-php/src/SupplyChain/SupplyChainClient.php',
    'Aws\\SupportApp\\Exception\\SupportAppException' => $vendorDir . '/aws/aws-sdk-php/src/SupportApp/Exception/SupportAppException.php',
    'Aws\\SupportApp\\SupportAppClient' => $vendorDir . '/aws/aws-sdk-php/src/SupportApp/SupportAppClient.php',
    'Aws\\Support\\Exception\\SupportException' => $vendorDir . '/aws/aws-sdk-php/src/Support/Exception/SupportException.php',
    'Aws\\Support\\SupportClient' => $vendorDir . '/aws/aws-sdk-php/src/Support/SupportClient.php',
    'Aws\\Swf\\Exception\\SwfException' => $vendorDir . '/aws/aws-sdk-php/src/Swf/Exception/SwfException.php',
    'Aws\\Swf\\SwfClient' => $vendorDir . '/aws/aws-sdk-php/src/Swf/SwfClient.php',
    'Aws\\Synthetics\\Exception\\SyntheticsException' => $vendorDir . '/aws/aws-sdk-php/src/Synthetics/Exception/SyntheticsException.php',
    'Aws\\Synthetics\\SyntheticsClient' => $vendorDir . '/aws/aws-sdk-php/src/Synthetics/SyntheticsClient.php',
    'Aws\\TaxSettings\\Exception\\TaxSettingsException' => $vendorDir . '/aws/aws-sdk-php/src/TaxSettings/Exception/TaxSettingsException.php',
    'Aws\\TaxSettings\\TaxSettingsClient' => $vendorDir . '/aws/aws-sdk-php/src/TaxSettings/TaxSettingsClient.php',
    'Aws\\Textract\\Exception\\TextractException' => $vendorDir . '/aws/aws-sdk-php/src/Textract/Exception/TextractException.php',
    'Aws\\Textract\\TextractClient' => $vendorDir . '/aws/aws-sdk-php/src/Textract/TextractClient.php',
    'Aws\\TimestreamInfluxDB\\Exception\\TimestreamInfluxDBException' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamInfluxDB/Exception/TimestreamInfluxDBException.php',
    'Aws\\TimestreamInfluxDB\\TimestreamInfluxDBClient' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamInfluxDB/TimestreamInfluxDBClient.php',
    'Aws\\TimestreamQuery\\Exception\\TimestreamQueryException' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamQuery/Exception/TimestreamQueryException.php',
    'Aws\\TimestreamQuery\\TimestreamQueryClient' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamQuery/TimestreamQueryClient.php',
    'Aws\\TimestreamWrite\\Exception\\TimestreamWriteException' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamWrite/Exception/TimestreamWriteException.php',
    'Aws\\TimestreamWrite\\TimestreamWriteClient' => $vendorDir . '/aws/aws-sdk-php/src/TimestreamWrite/TimestreamWriteClient.php',
    'Aws\\Tnb\\Exception\\TnbException' => $vendorDir . '/aws/aws-sdk-php/src/Tnb/Exception/TnbException.php',
    'Aws\\Tnb\\TnbClient' => $vendorDir . '/aws/aws-sdk-php/src/Tnb/TnbClient.php',
    'Aws\\Token\\BearerTokenAuthorization' => $vendorDir . '/aws/aws-sdk-php/src/Token/BearerTokenAuthorization.php',
    'Aws\\Token\\ParsesIniTrait' => $vendorDir . '/aws/aws-sdk-php/src/Token/ParsesIniTrait.php',
    'Aws\\Token\\RefreshableTokenProviderInterface' => $vendorDir . '/aws/aws-sdk-php/src/Token/RefreshableTokenProviderInterface.php',
    'Aws\\Token\\SsoToken' => $vendorDir . '/aws/aws-sdk-php/src/Token/SsoToken.php',
    'Aws\\Token\\SsoTokenProvider' => $vendorDir . '/aws/aws-sdk-php/src/Token/SsoTokenProvider.php',
    'Aws\\Token\\Token' => $vendorDir . '/aws/aws-sdk-php/src/Token/Token.php',
    'Aws\\Token\\TokenAuthorization' => $vendorDir . '/aws/aws-sdk-php/src/Token/TokenAuthorization.php',
    'Aws\\Token\\TokenInterface' => $vendorDir . '/aws/aws-sdk-php/src/Token/TokenInterface.php',
    'Aws\\Token\\TokenProvider' => $vendorDir . '/aws/aws-sdk-php/src/Token/TokenProvider.php',
    'Aws\\TraceMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/TraceMiddleware.php',
    'Aws\\TranscribeService\\Exception\\TranscribeServiceException' => $vendorDir . '/aws/aws-sdk-php/src/TranscribeService/Exception/TranscribeServiceException.php',
    'Aws\\TranscribeService\\TranscribeServiceClient' => $vendorDir . '/aws/aws-sdk-php/src/TranscribeService/TranscribeServiceClient.php',
    'Aws\\Transfer\\Exception\\TransferException' => $vendorDir . '/aws/aws-sdk-php/src/Transfer/Exception/TransferException.php',
    'Aws\\Transfer\\TransferClient' => $vendorDir . '/aws/aws-sdk-php/src/Transfer/TransferClient.php',
    'Aws\\Translate\\Exception\\TranslateException' => $vendorDir . '/aws/aws-sdk-php/src/Translate/Exception/TranslateException.php',
    'Aws\\Translate\\TranslateClient' => $vendorDir . '/aws/aws-sdk-php/src/Translate/TranslateClient.php',
    'Aws\\TrustedAdvisor\\Exception\\TrustedAdvisorException' => $vendorDir . '/aws/aws-sdk-php/src/TrustedAdvisor/Exception/TrustedAdvisorException.php',
    'Aws\\TrustedAdvisor\\TrustedAdvisorClient' => $vendorDir . '/aws/aws-sdk-php/src/TrustedAdvisor/TrustedAdvisorClient.php',
    'Aws\\UserAgentMiddleware' => $vendorDir . '/aws/aws-sdk-php/src/UserAgentMiddleware.php',
    'Aws\\VPCLattice\\Exception\\VPCLatticeException' => $vendorDir . '/aws/aws-sdk-php/src/VPCLattice/Exception/VPCLatticeException.php',
    'Aws\\VPCLattice\\VPCLatticeClient' => $vendorDir . '/aws/aws-sdk-php/src/VPCLattice/VPCLatticeClient.php',
    'Aws\\VerifiedPermissions\\Exception\\VerifiedPermissionsException' => $vendorDir . '/aws/aws-sdk-php/src/VerifiedPermissions/Exception/VerifiedPermissionsException.php',
    'Aws\\VerifiedPermissions\\VerifiedPermissionsClient' => $vendorDir . '/aws/aws-sdk-php/src/VerifiedPermissions/VerifiedPermissionsClient.php',
    'Aws\\VoiceID\\Exception\\VoiceIDException' => $vendorDir . '/aws/aws-sdk-php/src/VoiceID/Exception/VoiceIDException.php',
    'Aws\\VoiceID\\VoiceIDClient' => $vendorDir . '/aws/aws-sdk-php/src/VoiceID/VoiceIDClient.php',
    'Aws\\WAFV2\\Exception\\WAFV2Exception' => $vendorDir . '/aws/aws-sdk-php/src/WAFV2/Exception/WAFV2Exception.php',
    'Aws\\WAFV2\\WAFV2Client' => $vendorDir . '/aws/aws-sdk-php/src/WAFV2/WAFV2Client.php',
    'Aws\\WafRegional\\Exception\\WafRegionalException' => $vendorDir . '/aws/aws-sdk-php/src/WafRegional/Exception/WafRegionalException.php',
    'Aws\\WafRegional\\WafRegionalClient' => $vendorDir . '/aws/aws-sdk-php/src/WafRegional/WafRegionalClient.php',
    'Aws\\Waf\\Exception\\WafException' => $vendorDir . '/aws/aws-sdk-php/src/Waf/Exception/WafException.php',
    'Aws\\Waf\\WafClient' => $vendorDir . '/aws/aws-sdk-php/src/Waf/WafClient.php',
    'Aws\\Waiter' => $vendorDir . '/aws/aws-sdk-php/src/Waiter.php',
    'Aws\\WellArchitected\\Exception\\WellArchitectedException' => $vendorDir . '/aws/aws-sdk-php/src/WellArchitected/Exception/WellArchitectedException.php',
    'Aws\\WellArchitected\\WellArchitectedClient' => $vendorDir . '/aws/aws-sdk-php/src/WellArchitected/WellArchitectedClient.php',
    'Aws\\WorkDocs\\Exception\\WorkDocsException' => $vendorDir . '/aws/aws-sdk-php/src/WorkDocs/Exception/WorkDocsException.php',
    'Aws\\WorkDocs\\WorkDocsClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkDocs/WorkDocsClient.php',
    'Aws\\WorkMailMessageFlow\\Exception\\WorkMailMessageFlowException' => $vendorDir . '/aws/aws-sdk-php/src/WorkMailMessageFlow/Exception/WorkMailMessageFlowException.php',
    'Aws\\WorkMailMessageFlow\\WorkMailMessageFlowClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkMailMessageFlow/WorkMailMessageFlowClient.php',
    'Aws\\WorkMail\\Exception\\WorkMailException' => $vendorDir . '/aws/aws-sdk-php/src/WorkMail/Exception/WorkMailException.php',
    'Aws\\WorkMail\\WorkMailClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkMail/WorkMailClient.php',
    'Aws\\WorkSpacesThinClient\\Exception\\WorkSpacesThinClientException' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpacesThinClient/Exception/WorkSpacesThinClientException.php',
    'Aws\\WorkSpacesThinClient\\WorkSpacesThinClientClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpacesThinClient/WorkSpacesThinClientClient.php',
    'Aws\\WorkSpacesWeb\\Exception\\WorkSpacesWebException' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpacesWeb/Exception/WorkSpacesWebException.php',
    'Aws\\WorkSpacesWeb\\WorkSpacesWebClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpacesWeb/WorkSpacesWebClient.php',
    'Aws\\WorkSpaces\\Exception\\WorkSpacesException' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpaces/Exception/WorkSpacesException.php',
    'Aws\\WorkSpaces\\WorkSpacesClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkSpaces/WorkSpacesClient.php',
    'Aws\\WorkspacesInstances\\Exception\\WorkspacesInstancesException' => $vendorDir . '/aws/aws-sdk-php/src/WorkspacesInstances/Exception/WorkspacesInstancesException.php',
    'Aws\\WorkspacesInstances\\WorkspacesInstancesClient' => $vendorDir . '/aws/aws-sdk-php/src/WorkspacesInstances/WorkspacesInstancesClient.php',
    'Aws\\WrappedHttpHandler' => $vendorDir . '/aws/aws-sdk-php/src/WrappedHttpHandler.php',
    'Aws\\XRay\\Exception\\XRayException' => $vendorDir . '/aws/aws-sdk-php/src/XRay/Exception/XRayException.php',
    'Aws\\XRay\\XRayClient' => $vendorDir . '/aws/aws-sdk-php/src/XRay/XRayClient.php',
    'Aws\\drs\\Exception\\drsException' => $vendorDir . '/aws/aws-sdk-php/src/drs/Exception/drsException.php',
    'Aws\\drs\\drsClient' => $vendorDir . '/aws/aws-sdk-php/src/drs/drsClient.php',
    'Aws\\finspace\\Exception\\finspaceException' => $vendorDir . '/aws/aws-sdk-php/src/finspace/Exception/finspaceException.php',
    'Aws\\finspace\\finspaceClient' => $vendorDir . '/aws/aws-sdk-php/src/finspace/finspaceClient.php',
    'Aws\\imagebuilder\\Exception\\imagebuilderException' => $vendorDir . '/aws/aws-sdk-php/src/imagebuilder/Exception/imagebuilderException.php',
    'Aws\\imagebuilder\\imagebuilderClient' => $vendorDir . '/aws/aws-sdk-php/src/imagebuilder/imagebuilderClient.php',
    'Aws\\ivschat\\Exception\\ivschatException' => $vendorDir . '/aws/aws-sdk-php/src/ivschat/Exception/ivschatException.php',
    'Aws\\ivschat\\ivschatClient' => $vendorDir . '/aws/aws-sdk-php/src/ivschat/ivschatClient.php',
    'Aws\\kendra\\Exception\\kendraException' => $vendorDir . '/aws/aws-sdk-php/src/kendra/Exception/kendraException.php',
    'Aws\\kendra\\kendraClient' => $vendorDir . '/aws/aws-sdk-php/src/kendra/kendraClient.php',
    'Aws\\mgn\\Exception\\mgnException' => $vendorDir . '/aws/aws-sdk-php/src/mgn/Exception/mgnException.php',
    'Aws\\mgn\\mgnClient' => $vendorDir . '/aws/aws-sdk-php/src/mgn/mgnClient.php',
    'Aws\\signer\\Exception\\signerException' => $vendorDir . '/aws/aws-sdk-php/src/signer/Exception/signerException.php',
    'Aws\\signer\\signerClient' => $vendorDir . '/aws/aws-sdk-php/src/signer/signerClient.php',
    'Com\\Tecnick\\Barcode\\Barcode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Barcode.php',
    'Com\\Tecnick\\Barcode\\Exception' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Exception.php',
    'Com\\Tecnick\\Barcode\\Type' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type.php',
    'Com\\Tecnick\\Barcode\\Type\\Convert' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Convert.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Codabar' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Codabar.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeNineThree' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeNineThree.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneOne' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneOne.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneTwoEight' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneTwoEight.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneTwoEight\\CodeOneTwoEightA' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneTwoEight/CodeOneTwoEightA.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneTwoEight\\CodeOneTwoEightB' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneTwoEight/CodeOneTwoEightB.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneTwoEight\\CodeOneTwoEightC' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneTwoEight/CodeOneTwoEightC.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeOneTwoEight\\Process' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeOneTwoEight/Process.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeThreeNine' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeThreeNine.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeThreeNineCheck' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeThreeNineCheck.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeThreeNineExt' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeThreeNineExt.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\CodeThreeNineExtCheck' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/CodeThreeNineExtCheck.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\EanEight' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/EanEight.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\EanFive' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/EanFive.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\EanOneThree' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/EanOneThree.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\EanTwo' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/EanTwo.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Imb' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Imb.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\ImbPre' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/ImbPre.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\InterleavedTwoOfFive' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/InterleavedTwoOfFive.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\InterleavedTwoOfFiveCheck' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/InterleavedTwoOfFiveCheck.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\KlantIndex' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/KlantIndex.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Msi' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Msi.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\MsiCheck' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/MsiCheck.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Pharma' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Pharma.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\PharmaTwoTracks' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/PharmaTwoTracks.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Planet' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Planet.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Postnet' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Postnet.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\Raw' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/Raw.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\RoyalMailFourCc' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/RoyalMailFourCc.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\StandardTwoOfFive' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/StandardTwoOfFive.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\StandardTwoOfFiveCheck' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/StandardTwoOfFiveCheck.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\UpcA' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/UpcA.php',
    'Com\\Tecnick\\Barcode\\Type\\Linear\\UpcE' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Linear/UpcE.php',
    'Com\\Tecnick\\Barcode\\Type\\Raw' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Raw.php',
    'Com\\Tecnick\\Barcode\\Type\\Square' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\Bitstream' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/Bitstream.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\Codeword' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/Codeword.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\Data' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/Data.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\Encode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/Encode.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\ErrorCorrection' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/ErrorCorrection.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Aztec\\Layers' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Aztec/Layers.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\Data' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/Data.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\Encode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/Encode.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\EncodeTxt' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/EncodeTxt.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\ErrorCorrection' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/ErrorCorrection.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\Modes' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/Modes.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\Placement' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/Placement.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Datamatrix\\Steps' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Datamatrix/Steps.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\PdfFourOneSeven' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/PdfFourOneSeven.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\PdfFourOneSeven\\Compaction' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/PdfFourOneSeven/Compaction.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\PdfFourOneSeven\\Data' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/PdfFourOneSeven/Data.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\PdfFourOneSeven\\Sequence' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/PdfFourOneSeven/Sequence.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\ByteStream' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/ByteStream.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Data' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Data.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Encode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Encode.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Encoder' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Encoder.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\EncodingMode' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/EncodingMode.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Estimate' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Estimate.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Init' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Init.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\InputItem' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/InputItem.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Mask' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Mask.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\MaskNum' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/MaskNum.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Spec' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Spec.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\SpecRs' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/SpecRs.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\QrCode\\Split' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/QrCode/Split.php',
    'Com\\Tecnick\\Barcode\\Type\\Square\\Raw' => $vendorDir . '/tecnickcom/tc-lib-barcode/src/Type/Square/Raw.php',
    'Com\\Tecnick\\Color\\Css' => $vendorDir . '/tecnickcom/tc-lib-color/src/Css.php',
    'Com\\Tecnick\\Color\\Exception' => $vendorDir . '/tecnickcom/tc-lib-color/src/Exception.php',
    'Com\\Tecnick\\Color\\Model' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model.php',
    'Com\\Tecnick\\Color\\Model\\Cmyk' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model/Cmyk.php',
    'Com\\Tecnick\\Color\\Model\\Gray' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model/Gray.php',
    'Com\\Tecnick\\Color\\Model\\Hsl' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model/Hsl.php',
    'Com\\Tecnick\\Color\\Model\\Rgb' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model/Rgb.php',
    'Com\\Tecnick\\Color\\Model\\Template' => $vendorDir . '/tecnickcom/tc-lib-color/src/Model/Template.php',
    'Com\\Tecnick\\Color\\Pdf' => $vendorDir . '/tecnickcom/tc-lib-color/src/Pdf.php',
    'Com\\Tecnick\\Color\\Spot' => $vendorDir . '/tecnickcom/tc-lib-color/src/Spot.php',
    'Com\\Tecnick\\Color\\Web' => $vendorDir . '/tecnickcom/tc-lib-color/src/Web.php',
    'Complex\\Complex' => $vendorDir . '/markbaker/complex/classes/src/Complex.php',
    'Complex\\Exception' => $vendorDir . '/markbaker/complex/classes/src/Exception.php',
    'Complex\\Functions' => $vendorDir . '/markbaker/complex/classes/src/Functions.php',
    'Complex\\Operations' => $vendorDir . '/markbaker/complex/classes/src/Operations.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Pcre\\MatchAllResult' => $vendorDir . '/composer/pcre/src/MatchAllResult.php',
    'Composer\\Pcre\\MatchAllStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchAllStrictGroupsResult.php',
    'Composer\\Pcre\\MatchAllWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchAllWithOffsetsResult.php',
    'Composer\\Pcre\\MatchResult' => $vendorDir . '/composer/pcre/src/MatchResult.php',
    'Composer\\Pcre\\MatchStrictGroupsResult' => $vendorDir . '/composer/pcre/src/MatchStrictGroupsResult.php',
    'Composer\\Pcre\\MatchWithOffsetsResult' => $vendorDir . '/composer/pcre/src/MatchWithOffsetsResult.php',
    'Composer\\Pcre\\PHPStan\\InvalidRegexPatternRule' => $vendorDir . '/composer/pcre/src/PHPStan/InvalidRegexPatternRule.php',
    'Composer\\Pcre\\PHPStan\\PregMatchFlags' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchFlags.php',
    'Composer\\Pcre\\PHPStan\\PregMatchParameterOutTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchParameterOutTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\PregMatchTypeSpecifyingExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregMatchTypeSpecifyingExtension.php',
    'Composer\\Pcre\\PHPStan\\PregReplaceCallbackClosureTypeExtension' => $vendorDir . '/composer/pcre/src/PHPStan/PregReplaceCallbackClosureTypeExtension.php',
    'Composer\\Pcre\\PHPStan\\UnsafeStrictGroupsCallRule' => $vendorDir . '/composer/pcre/src/PHPStan/UnsafeStrictGroupsCallRule.php',
    'Composer\\Pcre\\PcreException' => $vendorDir . '/composer/pcre/src/PcreException.php',
    'Composer\\Pcre\\Preg' => $vendorDir . '/composer/pcre/src/Preg.php',
    'Composer\\Pcre\\Regex' => $vendorDir . '/composer/pcre/src/Regex.php',
    'Composer\\Pcre\\ReplaceResult' => $vendorDir . '/composer/pcre/src/ReplaceResult.php',
    'Composer\\Pcre\\UnexpectedNullMatchException' => $vendorDir . '/composer/pcre/src/UnexpectedNullMatchException.php',
    'Doctrine\\Common\\Annotations\\Annotation' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation.php',
    'Doctrine\\Common\\Annotations\\AnnotationException' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationException.php',
    'Doctrine\\Common\\Annotations\\AnnotationReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationReader.php',
    'Doctrine\\Common\\Annotations\\AnnotationRegistry' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationRegistry.php',
    'Doctrine\\Common\\Annotations\\Annotation\\Attribute' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Attribute.php',
    'Doctrine\\Common\\Annotations\\Annotation\\Attributes' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Attributes.php',
    'Doctrine\\Common\\Annotations\\Annotation\\Enum' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Enum.php',
    'Doctrine\\Common\\Annotations\\Annotation\\IgnoreAnnotation' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/IgnoreAnnotation.php',
    'Doctrine\\Common\\Annotations\\Annotation\\NamedArgumentConstructor' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/NamedArgumentConstructor.php',
    'Doctrine\\Common\\Annotations\\Annotation\\Required' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Required.php',
    'Doctrine\\Common\\Annotations\\Annotation\\Target' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Target.php',
    'Doctrine\\Common\\Annotations\\CachedReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/CachedReader.php',
    'Doctrine\\Common\\Annotations\\DocLexer' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/DocLexer.php',
    'Doctrine\\Common\\Annotations\\DocParser' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/DocParser.php',
    'Doctrine\\Common\\Annotations\\FileCacheReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/FileCacheReader.php',
    'Doctrine\\Common\\Annotations\\ImplicitlyIgnoredAnnotationNames' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/ImplicitlyIgnoredAnnotationNames.php',
    'Doctrine\\Common\\Annotations\\IndexedReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/IndexedReader.php',
    'Doctrine\\Common\\Annotations\\NamedArgumentConstructorAnnotation' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/NamedArgumentConstructorAnnotation.php',
    'Doctrine\\Common\\Annotations\\PhpParser' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/PhpParser.php',
    'Doctrine\\Common\\Annotations\\PsrCachedReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/PsrCachedReader.php',
    'Doctrine\\Common\\Annotations\\Reader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/Reader.php',
    'Doctrine\\Common\\Annotations\\SimpleAnnotationReader' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/SimpleAnnotationReader.php',
    'Doctrine\\Common\\Annotations\\TokenParser' => $vendorDir . '/doctrine/annotations/lib/Doctrine/Common/Annotations/TokenParser.php',
    'Doctrine\\Common\\ClassLoader' => $vendorDir . '/doctrine/common/src/ClassLoader.php',
    'Doctrine\\Common\\CommonException' => $vendorDir . '/doctrine/common/src/CommonException.php',
    'Doctrine\\Common\\Comparable' => $vendorDir . '/doctrine/common/src/Comparable.php',
    'Doctrine\\Common\\EventArgs' => $vendorDir . '/doctrine/event-manager/src/EventArgs.php',
    'Doctrine\\Common\\EventManager' => $vendorDir . '/doctrine/event-manager/src/EventManager.php',
    'Doctrine\\Common\\EventSubscriber' => $vendorDir . '/doctrine/event-manager/src/EventSubscriber.php',
    'Doctrine\\Common\\Lexer\\AbstractLexer' => $vendorDir . '/doctrine/lexer/src/AbstractLexer.php',
    'Doctrine\\Common\\Lexer\\Token' => $vendorDir . '/doctrine/lexer/src/Token.php',
    'Doctrine\\Common\\Proxy\\AbstractProxyFactory' => $vendorDir . '/doctrine/common/src/Proxy/AbstractProxyFactory.php',
    'Doctrine\\Common\\Proxy\\Autoloader' => $vendorDir . '/doctrine/common/src/Proxy/Autoloader.php',
    'Doctrine\\Common\\Proxy\\Exception\\InvalidArgumentException' => $vendorDir . '/doctrine/common/src/Proxy/Exception/InvalidArgumentException.php',
    'Doctrine\\Common\\Proxy\\Exception\\OutOfBoundsException' => $vendorDir . '/doctrine/common/src/Proxy/Exception/OutOfBoundsException.php',
    'Doctrine\\Common\\Proxy\\Exception\\ProxyException' => $vendorDir . '/doctrine/common/src/Proxy/Exception/ProxyException.php',
    'Doctrine\\Common\\Proxy\\Exception\\UnexpectedValueException' => $vendorDir . '/doctrine/common/src/Proxy/Exception/UnexpectedValueException.php',
    'Doctrine\\Common\\Proxy\\Proxy' => $vendorDir . '/doctrine/common/src/Proxy/Proxy.php',
    'Doctrine\\Common\\Proxy\\ProxyDefinition' => $vendorDir . '/doctrine/common/src/Proxy/ProxyDefinition.php',
    'Doctrine\\Common\\Proxy\\ProxyGenerator' => $vendorDir . '/doctrine/common/src/Proxy/ProxyGenerator.php',
    'Doctrine\\Common\\Util\\ClassUtils' => $vendorDir . '/doctrine/common/src/Util/ClassUtils.php',
    'Doctrine\\Common\\Util\\Debug' => $vendorDir . '/doctrine/common/src/Util/Debug.php',
    'Doctrine\\Deprecations\\Deprecation' => $vendorDir . '/doctrine/deprecations/src/Deprecation.php',
    'Doctrine\\Deprecations\\PHPUnit\\VerifyDeprecations' => $vendorDir . '/doctrine/deprecations/src/PHPUnit/VerifyDeprecations.php',
    'Doctrine\\Persistence\\AbstractManagerRegistry' => $vendorDir . '/doctrine/persistence/src/Persistence/AbstractManagerRegistry.php',
    'Doctrine\\Persistence\\ConnectionRegistry' => $vendorDir . '/doctrine/persistence/src/Persistence/ConnectionRegistry.php',
    'Doctrine\\Persistence\\Event\\LifecycleEventArgs' => $vendorDir . '/doctrine/persistence/src/Persistence/Event/LifecycleEventArgs.php',
    'Doctrine\\Persistence\\Event\\LoadClassMetadataEventArgs' => $vendorDir . '/doctrine/persistence/src/Persistence/Event/LoadClassMetadataEventArgs.php',
    'Doctrine\\Persistence\\Event\\ManagerEventArgs' => $vendorDir . '/doctrine/persistence/src/Persistence/Event/ManagerEventArgs.php',
    'Doctrine\\Persistence\\Event\\OnClearEventArgs' => $vendorDir . '/doctrine/persistence/src/Persistence/Event/OnClearEventArgs.php',
    'Doctrine\\Persistence\\Event\\PreUpdateEventArgs' => $vendorDir . '/doctrine/persistence/src/Persistence/Event/PreUpdateEventArgs.php',
    'Doctrine\\Persistence\\ManagerRegistry' => $vendorDir . '/doctrine/persistence/src/Persistence/ManagerRegistry.php',
    'Doctrine\\Persistence\\Mapping\\AbstractClassMetadataFactory' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/AbstractClassMetadataFactory.php',
    'Doctrine\\Persistence\\Mapping\\ClassMetadata' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/ClassMetadata.php',
    'Doctrine\\Persistence\\Mapping\\ClassMetadataFactory' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/ClassMetadataFactory.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\ColocatedMappingDriver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/ColocatedMappingDriver.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\DefaultFileLocator' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/DefaultFileLocator.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\FileDriver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/FileDriver.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\FileLocator' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/FileLocator.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\MappingDriver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/MappingDriver.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\MappingDriverChain' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/MappingDriverChain.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\PHPDriver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/PHPDriver.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\StaticPHPDriver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/StaticPHPDriver.php',
    'Doctrine\\Persistence\\Mapping\\Driver\\SymfonyFileLocator' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/Driver/SymfonyFileLocator.php',
    'Doctrine\\Persistence\\Mapping\\MappingException' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/MappingException.php',
    'Doctrine\\Persistence\\Mapping\\ProxyClassNameResolver' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/ProxyClassNameResolver.php',
    'Doctrine\\Persistence\\Mapping\\ReflectionService' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/ReflectionService.php',
    'Doctrine\\Persistence\\Mapping\\RuntimeReflectionService' => $vendorDir . '/doctrine/persistence/src/Persistence/Mapping/RuntimeReflectionService.php',
    'Doctrine\\Persistence\\NotifyPropertyChanged' => $vendorDir . '/doctrine/persistence/src/Persistence/NotifyPropertyChanged.php',
    'Doctrine\\Persistence\\ObjectManager' => $vendorDir . '/doctrine/persistence/src/Persistence/ObjectManager.php',
    'Doctrine\\Persistence\\ObjectManagerDecorator' => $vendorDir . '/doctrine/persistence/src/Persistence/ObjectManagerDecorator.php',
    'Doctrine\\Persistence\\ObjectRepository' => $vendorDir . '/doctrine/persistence/src/Persistence/ObjectRepository.php',
    'Doctrine\\Persistence\\PropertyChangedListener' => $vendorDir . '/doctrine/persistence/src/Persistence/PropertyChangedListener.php',
    'Doctrine\\Persistence\\Proxy' => $vendorDir . '/doctrine/persistence/src/Persistence/Proxy.php',
    'Doctrine\\Persistence\\Reflection\\EnumReflectionProperty' => $vendorDir . '/doctrine/persistence/src/Persistence/Reflection/EnumReflectionProperty.php',
    'Doctrine\\Persistence\\Reflection\\RuntimeReflectionProperty' => $vendorDir . '/doctrine/persistence/src/Persistence/Reflection/RuntimeReflectionProperty.php',
    'Doctrine\\Persistence\\Reflection\\TypedNoDefaultReflectionProperty' => $vendorDir . '/doctrine/persistence/src/Persistence/Reflection/TypedNoDefaultReflectionProperty.php',
    'Dompdf\\Adapter\\CPDF' => $vendorDir . '/dompdf/dompdf/src/Adapter/CPDF.php',
    'Dompdf\\Adapter\\GD' => $vendorDir . '/dompdf/dompdf/src/Adapter/GD.php',
    'Dompdf\\Adapter\\PDFLib' => $vendorDir . '/dompdf/dompdf/src/Adapter/PDFLib.php',
    'Dompdf\\Canvas' => $vendorDir . '/dompdf/dompdf/src/Canvas.php',
    'Dompdf\\CanvasFactory' => $vendorDir . '/dompdf/dompdf/src/CanvasFactory.php',
    'Dompdf\\Cellmap' => $vendorDir . '/dompdf/dompdf/src/Cellmap.php',
    'Dompdf\\Cpdf' => $vendorDir . '/dompdf/dompdf/lib/Cpdf.php',
    'Dompdf\\Css\\AttributeTranslator' => $vendorDir . '/dompdf/dompdf/src/Css/AttributeTranslator.php',
    'Dompdf\\Css\\Color' => $vendorDir . '/dompdf/dompdf/src/Css/Color.php',
    'Dompdf\\Css\\Style' => $vendorDir . '/dompdf/dompdf/src/Css/Style.php',
    'Dompdf\\Css\\Stylesheet' => $vendorDir . '/dompdf/dompdf/src/Css/Stylesheet.php',
    'Dompdf\\Dompdf' => $vendorDir . '/dompdf/dompdf/src/Dompdf.php',
    'Dompdf\\Exception' => $vendorDir . '/dompdf/dompdf/src/Exception.php',
    'Dompdf\\Exception\\ImageException' => $vendorDir . '/dompdf/dompdf/src/Exception/ImageException.php',
    'Dompdf\\FontMetrics' => $vendorDir . '/dompdf/dompdf/src/FontMetrics.php',
    'Dompdf\\Frame' => $vendorDir . '/dompdf/dompdf/src/Frame.php',
    'Dompdf\\FrameDecorator\\AbstractFrameDecorator' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/AbstractFrameDecorator.php',
    'Dompdf\\FrameDecorator\\Block' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Block.php',
    'Dompdf\\FrameDecorator\\Image' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Image.php',
    'Dompdf\\FrameDecorator\\Inline' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Inline.php',
    'Dompdf\\FrameDecorator\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/ListBullet.php',
    'Dompdf\\FrameDecorator\\ListBulletImage' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/ListBulletImage.php',
    'Dompdf\\FrameDecorator\\NullFrameDecorator' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/NullFrameDecorator.php',
    'Dompdf\\FrameDecorator\\Page' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Page.php',
    'Dompdf\\FrameDecorator\\Table' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Table.php',
    'Dompdf\\FrameDecorator\\TableCell' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableCell.php',
    'Dompdf\\FrameDecorator\\TableRow' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableRow.php',
    'Dompdf\\FrameDecorator\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableRowGroup.php',
    'Dompdf\\FrameDecorator\\Text' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Text.php',
    'Dompdf\\FrameReflower\\AbstractFrameReflower' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/AbstractFrameReflower.php',
    'Dompdf\\FrameReflower\\Block' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Block.php',
    'Dompdf\\FrameReflower\\Image' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Image.php',
    'Dompdf\\FrameReflower\\Inline' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Inline.php',
    'Dompdf\\FrameReflower\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/ListBullet.php',
    'Dompdf\\FrameReflower\\NullFrameReflower' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/NullFrameReflower.php',
    'Dompdf\\FrameReflower\\Page' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Page.php',
    'Dompdf\\FrameReflower\\Table' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Table.php',
    'Dompdf\\FrameReflower\\TableCell' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableCell.php',
    'Dompdf\\FrameReflower\\TableRow' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableRow.php',
    'Dompdf\\FrameReflower\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableRowGroup.php',
    'Dompdf\\FrameReflower\\Text' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Text.php',
    'Dompdf\\Frame\\Factory' => $vendorDir . '/dompdf/dompdf/src/Frame/Factory.php',
    'Dompdf\\Frame\\FrameListIterator' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameListIterator.php',
    'Dompdf\\Frame\\FrameTree' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameTree.php',
    'Dompdf\\Frame\\FrameTreeIterator' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameTreeIterator.php',
    'Dompdf\\Helpers' => $vendorDir . '/dompdf/dompdf/src/Helpers.php',
    'Dompdf\\Image\\Cache' => $vendorDir . '/dompdf/dompdf/src/Image/Cache.php',
    'Dompdf\\JavascriptEmbedder' => $vendorDir . '/dompdf/dompdf/src/JavascriptEmbedder.php',
    'Dompdf\\LineBox' => $vendorDir . '/dompdf/dompdf/src/LineBox.php',
    'Dompdf\\Options' => $vendorDir . '/dompdf/dompdf/src/Options.php',
    'Dompdf\\PhpEvaluator' => $vendorDir . '/dompdf/dompdf/src/PhpEvaluator.php',
    'Dompdf\\Positioner\\Absolute' => $vendorDir . '/dompdf/dompdf/src/Positioner/Absolute.php',
    'Dompdf\\Positioner\\AbstractPositioner' => $vendorDir . '/dompdf/dompdf/src/Positioner/AbstractPositioner.php',
    'Dompdf\\Positioner\\Block' => $vendorDir . '/dompdf/dompdf/src/Positioner/Block.php',
    'Dompdf\\Positioner\\Fixed' => $vendorDir . '/dompdf/dompdf/src/Positioner/Fixed.php',
    'Dompdf\\Positioner\\Inline' => $vendorDir . '/dompdf/dompdf/src/Positioner/Inline.php',
    'Dompdf\\Positioner\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/Positioner/ListBullet.php',
    'Dompdf\\Positioner\\NullPositioner' => $vendorDir . '/dompdf/dompdf/src/Positioner/NullPositioner.php',
    'Dompdf\\Positioner\\TableCell' => $vendorDir . '/dompdf/dompdf/src/Positioner/TableCell.php',
    'Dompdf\\Positioner\\TableRow' => $vendorDir . '/dompdf/dompdf/src/Positioner/TableRow.php',
    'Dompdf\\Renderer' => $vendorDir . '/dompdf/dompdf/src/Renderer.php',
    'Dompdf\\Renderer\\AbstractRenderer' => $vendorDir . '/dompdf/dompdf/src/Renderer/AbstractRenderer.php',
    'Dompdf\\Renderer\\Block' => $vendorDir . '/dompdf/dompdf/src/Renderer/Block.php',
    'Dompdf\\Renderer\\Image' => $vendorDir . '/dompdf/dompdf/src/Renderer/Image.php',
    'Dompdf\\Renderer\\Inline' => $vendorDir . '/dompdf/dompdf/src/Renderer/Inline.php',
    'Dompdf\\Renderer\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/Renderer/ListBullet.php',
    'Dompdf\\Renderer\\TableCell' => $vendorDir . '/dompdf/dompdf/src/Renderer/TableCell.php',
    'Dompdf\\Renderer\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/Renderer/TableRowGroup.php',
    'Dompdf\\Renderer\\Text' => $vendorDir . '/dompdf/dompdf/src/Renderer/Text.php',
    'FontLib\\AdobeFontMetrics' => $vendorDir . '/phenx/php-font-lib/src/FontLib/AdobeFontMetrics.php',
    'FontLib\\BinaryStream' => $vendorDir . '/phenx/php-font-lib/src/FontLib/BinaryStream.php',
    'FontLib\\EOT\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EOT/File.php',
    'FontLib\\EOT\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EOT/Header.php',
    'FontLib\\EncodingMap' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EncodingMap.php',
    'FontLib\\Exception\\FontNotFoundException' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Exception/FontNotFoundException.php',
    'FontLib\\Font' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Font.php',
    'FontLib\\Glyph\\Outline' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/Outline.php',
    'FontLib\\Glyph\\OutlineComponent' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineComponent.php',
    'FontLib\\Glyph\\OutlineComposite' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineComposite.php',
    'FontLib\\Glyph\\OutlineSimple' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineSimple.php',
    'FontLib\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Header.php',
    'FontLib\\OpenType\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/OpenType/File.php',
    'FontLib\\OpenType\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/OpenType/TableDirectoryEntry.php',
    'FontLib\\Table\\DirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/DirectoryEntry.php',
    'FontLib\\Table\\Table' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Table.php',
    'FontLib\\Table\\Type\\cmap' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/cmap.php',
    'FontLib\\Table\\Type\\cvt' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/cvt.php',
    'FontLib\\Table\\Type\\fpgm' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/fpgm.php',
    'FontLib\\Table\\Type\\glyf' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/glyf.php',
    'FontLib\\Table\\Type\\head' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/head.php',
    'FontLib\\Table\\Type\\hhea' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/hhea.php',
    'FontLib\\Table\\Type\\hmtx' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/hmtx.php',
    'FontLib\\Table\\Type\\kern' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/kern.php',
    'FontLib\\Table\\Type\\loca' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/loca.php',
    'FontLib\\Table\\Type\\maxp' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/maxp.php',
    'FontLib\\Table\\Type\\name' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/name.php',
    'FontLib\\Table\\Type\\nameRecord' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/nameRecord.php',
    'FontLib\\Table\\Type\\os2' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/os2.php',
    'FontLib\\Table\\Type\\post' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/post.php',
    'FontLib\\Table\\Type\\prep' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/prep.php',
    'FontLib\\TrueType\\Collection' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/Collection.php',
    'FontLib\\TrueType\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/File.php',
    'FontLib\\TrueType\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/Header.php',
    'FontLib\\TrueType\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/TableDirectoryEntry.php',
    'FontLib\\WOFF\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/File.php',
    'FontLib\\WOFF\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/Header.php',
    'FontLib\\WOFF\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/TableDirectoryEntry.php',
    'GuzzleHttp\\BodySummarizer' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizer.php',
    'GuzzleHttp\\BodySummarizerInterface' => $vendorDir . '/guzzlehttp/guzzle/src/BodySummarizerInterface.php',
    'GuzzleHttp\\Client' => $vendorDir . '/guzzlehttp/guzzle/src/Client.php',
    'GuzzleHttp\\ClientInterface' => $vendorDir . '/guzzlehttp/guzzle/src/ClientInterface.php',
    'GuzzleHttp\\ClientTrait' => $vendorDir . '/guzzlehttp/guzzle/src/ClientTrait.php',
    'GuzzleHttp\\Cookie\\CookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJar.php',
    'GuzzleHttp\\Cookie\\CookieJarInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php',
    'GuzzleHttp\\Cookie\\FileCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php',
    'GuzzleHttp\\Cookie\\SessionCookieJar' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php',
    'GuzzleHttp\\Cookie\\SetCookie' => $vendorDir . '/guzzlehttp/guzzle/src/Cookie/SetCookie.php',
    'GuzzleHttp\\Exception\\BadResponseException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/BadResponseException.php',
    'GuzzleHttp\\Exception\\ClientException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ClientException.php',
    'GuzzleHttp\\Exception\\ConnectException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ConnectException.php',
    'GuzzleHttp\\Exception\\GuzzleException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/GuzzleException.php',
    'GuzzleHttp\\Exception\\InvalidArgumentException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php',
    'GuzzleHttp\\Exception\\RequestException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/RequestException.php',
    'GuzzleHttp\\Exception\\ServerException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/ServerException.php',
    'GuzzleHttp\\Exception\\TooManyRedirectsException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php',
    'GuzzleHttp\\Exception\\TransferException' => $vendorDir . '/guzzlehttp/guzzle/src/Exception/TransferException.php',
    'GuzzleHttp\\HandlerStack' => $vendorDir . '/guzzlehttp/guzzle/src/HandlerStack.php',
    'GuzzleHttp\\Handler\\CurlFactory' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactory.php',
    'GuzzleHttp\\Handler\\CurlFactoryInterface' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php',
    'GuzzleHttp\\Handler\\CurlHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlHandler.php',
    'GuzzleHttp\\Handler\\CurlMultiHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php',
    'GuzzleHttp\\Handler\\EasyHandle' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/EasyHandle.php',
    'GuzzleHttp\\Handler\\HeaderProcessor' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php',
    'GuzzleHttp\\Handler\\MockHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/MockHandler.php',
    'GuzzleHttp\\Handler\\Proxy' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/Proxy.php',
    'GuzzleHttp\\Handler\\StreamHandler' => $vendorDir . '/guzzlehttp/guzzle/src/Handler/StreamHandler.php',
    'GuzzleHttp\\MessageFormatter' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatter.php',
    'GuzzleHttp\\MessageFormatterInterface' => $vendorDir . '/guzzlehttp/guzzle/src/MessageFormatterInterface.php',
    'GuzzleHttp\\Middleware' => $vendorDir . '/guzzlehttp/guzzle/src/Middleware.php',
    'GuzzleHttp\\Pool' => $vendorDir . '/guzzlehttp/guzzle/src/Pool.php',
    'GuzzleHttp\\PrepareBodyMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php',
    'GuzzleHttp\\Promise\\AggregateException' => $vendorDir . '/guzzlehttp/promises/src/AggregateException.php',
    'GuzzleHttp\\Promise\\CancellationException' => $vendorDir . '/guzzlehttp/promises/src/CancellationException.php',
    'GuzzleHttp\\Promise\\Coroutine' => $vendorDir . '/guzzlehttp/promises/src/Coroutine.php',
    'GuzzleHttp\\Promise\\Create' => $vendorDir . '/guzzlehttp/promises/src/Create.php',
    'GuzzleHttp\\Promise\\Each' => $vendorDir . '/guzzlehttp/promises/src/Each.php',
    'GuzzleHttp\\Promise\\EachPromise' => $vendorDir . '/guzzlehttp/promises/src/EachPromise.php',
    'GuzzleHttp\\Promise\\FulfilledPromise' => $vendorDir . '/guzzlehttp/promises/src/FulfilledPromise.php',
    'GuzzleHttp\\Promise\\Is' => $vendorDir . '/guzzlehttp/promises/src/Is.php',
    'GuzzleHttp\\Promise\\Promise' => $vendorDir . '/guzzlehttp/promises/src/Promise.php',
    'GuzzleHttp\\Promise\\PromiseInterface' => $vendorDir . '/guzzlehttp/promises/src/PromiseInterface.php',
    'GuzzleHttp\\Promise\\PromisorInterface' => $vendorDir . '/guzzlehttp/promises/src/PromisorInterface.php',
    'GuzzleHttp\\Promise\\RejectedPromise' => $vendorDir . '/guzzlehttp/promises/src/RejectedPromise.php',
    'GuzzleHttp\\Promise\\RejectionException' => $vendorDir . '/guzzlehttp/promises/src/RejectionException.php',
    'GuzzleHttp\\Promise\\TaskQueue' => $vendorDir . '/guzzlehttp/promises/src/TaskQueue.php',
    'GuzzleHttp\\Promise\\TaskQueueInterface' => $vendorDir . '/guzzlehttp/promises/src/TaskQueueInterface.php',
    'GuzzleHttp\\Promise\\Utils' => $vendorDir . '/guzzlehttp/promises/src/Utils.php',
    'GuzzleHttp\\Psr7\\AppendStream' => $vendorDir . '/guzzlehttp/psr7/src/AppendStream.php',
    'GuzzleHttp\\Psr7\\BufferStream' => $vendorDir . '/guzzlehttp/psr7/src/BufferStream.php',
    'GuzzleHttp\\Psr7\\CachingStream' => $vendorDir . '/guzzlehttp/psr7/src/CachingStream.php',
    'GuzzleHttp\\Psr7\\DroppingStream' => $vendorDir . '/guzzlehttp/psr7/src/DroppingStream.php',
    'GuzzleHttp\\Psr7\\Exception\\MalformedUriException' => $vendorDir . '/guzzlehttp/psr7/src/Exception/MalformedUriException.php',
    'GuzzleHttp\\Psr7\\FnStream' => $vendorDir . '/guzzlehttp/psr7/src/FnStream.php',
    'GuzzleHttp\\Psr7\\Header' => $vendorDir . '/guzzlehttp/psr7/src/Header.php',
    'GuzzleHttp\\Psr7\\HttpFactory' => $vendorDir . '/guzzlehttp/psr7/src/HttpFactory.php',
    'GuzzleHttp\\Psr7\\InflateStream' => $vendorDir . '/guzzlehttp/psr7/src/InflateStream.php',
    'GuzzleHttp\\Psr7\\LazyOpenStream' => $vendorDir . '/guzzlehttp/psr7/src/LazyOpenStream.php',
    'GuzzleHttp\\Psr7\\LimitStream' => $vendorDir . '/guzzlehttp/psr7/src/LimitStream.php',
    'GuzzleHttp\\Psr7\\Message' => $vendorDir . '/guzzlehttp/psr7/src/Message.php',
    'GuzzleHttp\\Psr7\\MessageTrait' => $vendorDir . '/guzzlehttp/psr7/src/MessageTrait.php',
    'GuzzleHttp\\Psr7\\MimeType' => $vendorDir . '/guzzlehttp/psr7/src/MimeType.php',
    'GuzzleHttp\\Psr7\\MultipartStream' => $vendorDir . '/guzzlehttp/psr7/src/MultipartStream.php',
    'GuzzleHttp\\Psr7\\NoSeekStream' => $vendorDir . '/guzzlehttp/psr7/src/NoSeekStream.php',
    'GuzzleHttp\\Psr7\\PumpStream' => $vendorDir . '/guzzlehttp/psr7/src/PumpStream.php',
    'GuzzleHttp\\Psr7\\Query' => $vendorDir . '/guzzlehttp/psr7/src/Query.php',
    'GuzzleHttp\\Psr7\\Request' => $vendorDir . '/guzzlehttp/psr7/src/Request.php',
    'GuzzleHttp\\Psr7\\Response' => $vendorDir . '/guzzlehttp/psr7/src/Response.php',
    'GuzzleHttp\\Psr7\\Rfc7230' => $vendorDir . '/guzzlehttp/psr7/src/Rfc7230.php',
    'GuzzleHttp\\Psr7\\ServerRequest' => $vendorDir . '/guzzlehttp/psr7/src/ServerRequest.php',
    'GuzzleHttp\\Psr7\\Stream' => $vendorDir . '/guzzlehttp/psr7/src/Stream.php',
    'GuzzleHttp\\Psr7\\StreamDecoratorTrait' => $vendorDir . '/guzzlehttp/psr7/src/StreamDecoratorTrait.php',
    'GuzzleHttp\\Psr7\\StreamWrapper' => $vendorDir . '/guzzlehttp/psr7/src/StreamWrapper.php',
    'GuzzleHttp\\Psr7\\UploadedFile' => $vendorDir . '/guzzlehttp/psr7/src/UploadedFile.php',
    'GuzzleHttp\\Psr7\\Uri' => $vendorDir . '/guzzlehttp/psr7/src/Uri.php',
    'GuzzleHttp\\Psr7\\UriComparator' => $vendorDir . '/guzzlehttp/psr7/src/UriComparator.php',
    'GuzzleHttp\\Psr7\\UriNormalizer' => $vendorDir . '/guzzlehttp/psr7/src/UriNormalizer.php',
    'GuzzleHttp\\Psr7\\UriResolver' => $vendorDir . '/guzzlehttp/psr7/src/UriResolver.php',
    'GuzzleHttp\\Psr7\\Utils' => $vendorDir . '/guzzlehttp/psr7/src/Utils.php',
    'GuzzleHttp\\RedirectMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RedirectMiddleware.php',
    'GuzzleHttp\\RequestOptions' => $vendorDir . '/guzzlehttp/guzzle/src/RequestOptions.php',
    'GuzzleHttp\\RetryMiddleware' => $vendorDir . '/guzzlehttp/guzzle/src/RetryMiddleware.php',
    'GuzzleHttp\\TransferStats' => $vendorDir . '/guzzlehttp/guzzle/src/TransferStats.php',
    'GuzzleHttp\\Utils' => $vendorDir . '/guzzlehttp/guzzle/src/Utils.php',
    'HTMLPurifier' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier.php',
    'HTMLPurifier_Arborize' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Arborize.php',
    'HTMLPurifier_AttrCollections' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrCollections.php',
    'HTMLPurifier_AttrDef' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef.php',
    'HTMLPurifier_AttrDef_CSS' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS.php',
    'HTMLPurifier_AttrDef_CSS_AlphaValue' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/AlphaValue.php',
    'HTMLPurifier_AttrDef_CSS_Background' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Background.php',
    'HTMLPurifier_AttrDef_CSS_BackgroundPosition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/BackgroundPosition.php',
    'HTMLPurifier_AttrDef_CSS_Border' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Border.php',
    'HTMLPurifier_AttrDef_CSS_Color' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Color.php',
    'HTMLPurifier_AttrDef_CSS_Composite' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Composite.php',
    'HTMLPurifier_AttrDef_CSS_DenyElementDecorator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/DenyElementDecorator.php',
    'HTMLPurifier_AttrDef_CSS_Filter' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Filter.php',
    'HTMLPurifier_AttrDef_CSS_Font' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Font.php',
    'HTMLPurifier_AttrDef_CSS_FontFamily' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/FontFamily.php',
    'HTMLPurifier_AttrDef_CSS_Ident' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Ident.php',
    'HTMLPurifier_AttrDef_CSS_ImportantDecorator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/ImportantDecorator.php',
    'HTMLPurifier_AttrDef_CSS_Length' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Length.php',
    'HTMLPurifier_AttrDef_CSS_ListStyle' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/ListStyle.php',
    'HTMLPurifier_AttrDef_CSS_Multiple' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Multiple.php',
    'HTMLPurifier_AttrDef_CSS_Number' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Number.php',
    'HTMLPurifier_AttrDef_CSS_Percentage' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Percentage.php',
    'HTMLPurifier_AttrDef_CSS_Ratio' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/Ratio.php',
    'HTMLPurifier_AttrDef_CSS_TextDecoration' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/TextDecoration.php',
    'HTMLPurifier_AttrDef_CSS_URI' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/CSS/URI.php',
    'HTMLPurifier_AttrDef_Clone' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Clone.php',
    'HTMLPurifier_AttrDef_Enum' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Enum.php',
    'HTMLPurifier_AttrDef_HTML_Bool' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Bool.php',
    'HTMLPurifier_AttrDef_HTML_Class' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Class.php',
    'HTMLPurifier_AttrDef_HTML_Color' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Color.php',
    'HTMLPurifier_AttrDef_HTML_ContentEditable' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/ContentEditable.php',
    'HTMLPurifier_AttrDef_HTML_FrameTarget' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/FrameTarget.php',
    'HTMLPurifier_AttrDef_HTML_ID' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/ID.php',
    'HTMLPurifier_AttrDef_HTML_Length' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Length.php',
    'HTMLPurifier_AttrDef_HTML_LinkTypes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/LinkTypes.php',
    'HTMLPurifier_AttrDef_HTML_MultiLength' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/MultiLength.php',
    'HTMLPurifier_AttrDef_HTML_Nmtokens' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Nmtokens.php',
    'HTMLPurifier_AttrDef_HTML_Pixels' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/HTML/Pixels.php',
    'HTMLPurifier_AttrDef_Integer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Integer.php',
    'HTMLPurifier_AttrDef_Lang' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Lang.php',
    'HTMLPurifier_AttrDef_Switch' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Switch.php',
    'HTMLPurifier_AttrDef_Text' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/Text.php',
    'HTMLPurifier_AttrDef_URI' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI.php',
    'HTMLPurifier_AttrDef_URI_Email' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Email.php',
    'HTMLPurifier_AttrDef_URI_Email_SimpleCheck' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Email/SimpleCheck.php',
    'HTMLPurifier_AttrDef_URI_Host' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/Host.php',
    'HTMLPurifier_AttrDef_URI_IPv4' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/IPv4.php',
    'HTMLPurifier_AttrDef_URI_IPv6' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrDef/URI/IPv6.php',
    'HTMLPurifier_AttrTransform' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform.php',
    'HTMLPurifier_AttrTransform_Background' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Background.php',
    'HTMLPurifier_AttrTransform_BdoDir' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BdoDir.php',
    'HTMLPurifier_AttrTransform_BgColor' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BgColor.php',
    'HTMLPurifier_AttrTransform_BoolToCSS' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/BoolToCSS.php',
    'HTMLPurifier_AttrTransform_Border' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Border.php',
    'HTMLPurifier_AttrTransform_EnumToCSS' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/EnumToCSS.php',
    'HTMLPurifier_AttrTransform_ImgRequired' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ImgRequired.php',
    'HTMLPurifier_AttrTransform_ImgSpace' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ImgSpace.php',
    'HTMLPurifier_AttrTransform_Input' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Input.php',
    'HTMLPurifier_AttrTransform_Lang' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Lang.php',
    'HTMLPurifier_AttrTransform_Length' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Length.php',
    'HTMLPurifier_AttrTransform_Name' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Name.php',
    'HTMLPurifier_AttrTransform_NameSync' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/NameSync.php',
    'HTMLPurifier_AttrTransform_Nofollow' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Nofollow.php',
    'HTMLPurifier_AttrTransform_SafeEmbed' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeEmbed.php',
    'HTMLPurifier_AttrTransform_SafeObject' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeObject.php',
    'HTMLPurifier_AttrTransform_SafeParam' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/SafeParam.php',
    'HTMLPurifier_AttrTransform_ScriptRequired' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/ScriptRequired.php',
    'HTMLPurifier_AttrTransform_TargetBlank' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetBlank.php',
    'HTMLPurifier_AttrTransform_TargetNoopener' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetNoopener.php',
    'HTMLPurifier_AttrTransform_TargetNoreferrer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/TargetNoreferrer.php',
    'HTMLPurifier_AttrTransform_Textarea' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTransform/Textarea.php',
    'HTMLPurifier_AttrTypes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrTypes.php',
    'HTMLPurifier_AttrValidator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/AttrValidator.php',
    'HTMLPurifier_Bootstrap' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Bootstrap.php',
    'HTMLPurifier_CSSDefinition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/CSSDefinition.php',
    'HTMLPurifier_ChildDef' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef.php',
    'HTMLPurifier_ChildDef_Chameleon' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Chameleon.php',
    'HTMLPurifier_ChildDef_Custom' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Custom.php',
    'HTMLPurifier_ChildDef_Empty' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Empty.php',
    'HTMLPurifier_ChildDef_List' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/List.php',
    'HTMLPurifier_ChildDef_Optional' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Optional.php',
    'HTMLPurifier_ChildDef_Required' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Required.php',
    'HTMLPurifier_ChildDef_StrictBlockquote' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/StrictBlockquote.php',
    'HTMLPurifier_ChildDef_Table' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ChildDef/Table.php',
    'HTMLPurifier_Config' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Config.php',
    'HTMLPurifier_ConfigSchema' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema.php',
    'HTMLPurifier_ConfigSchema_Builder_ConfigSchema' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Builder/ConfigSchema.php',
    'HTMLPurifier_ConfigSchema_Builder_Xml' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Builder/Xml.php',
    'HTMLPurifier_ConfigSchema_Exception' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Exception.php',
    'HTMLPurifier_ConfigSchema_Interchange' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange.php',
    'HTMLPurifier_ConfigSchema_InterchangeBuilder' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/InterchangeBuilder.php',
    'HTMLPurifier_ConfigSchema_Interchange_Directive' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange/Directive.php',
    'HTMLPurifier_ConfigSchema_Interchange_Id' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Interchange/Id.php',
    'HTMLPurifier_ConfigSchema_Validator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/Validator.php',
    'HTMLPurifier_ConfigSchema_ValidatorAtom' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ConfigSchema/ValidatorAtom.php',
    'HTMLPurifier_ContentSets' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ContentSets.php',
    'HTMLPurifier_Context' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Context.php',
    'HTMLPurifier_Definition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Definition.php',
    'HTMLPurifier_DefinitionCache' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache.php',
    'HTMLPurifier_DefinitionCacheFactory' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCacheFactory.php',
    'HTMLPurifier_DefinitionCache_Decorator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator.php',
    'HTMLPurifier_DefinitionCache_Decorator_Cleanup' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator/Cleanup.php',
    'HTMLPurifier_DefinitionCache_Decorator_Memory' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Decorator/Memory.php',
    'HTMLPurifier_DefinitionCache_Null' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Null.php',
    'HTMLPurifier_DefinitionCache_Serializer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DefinitionCache/Serializer.php',
    'HTMLPurifier_Doctype' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Doctype.php',
    'HTMLPurifier_DoctypeRegistry' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/DoctypeRegistry.php',
    'HTMLPurifier_ElementDef' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ElementDef.php',
    'HTMLPurifier_Encoder' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Encoder.php',
    'HTMLPurifier_EntityLookup' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/EntityLookup.php',
    'HTMLPurifier_EntityParser' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/EntityParser.php',
    'HTMLPurifier_ErrorCollector' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ErrorCollector.php',
    'HTMLPurifier_ErrorStruct' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/ErrorStruct.php',
    'HTMLPurifier_Exception' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Exception.php',
    'HTMLPurifier_Filter' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Filter.php',
    'HTMLPurifier_Filter_ExtractStyleBlocks' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Filter/ExtractStyleBlocks.php',
    'HTMLPurifier_Filter_YouTube' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Filter/YouTube.php',
    'HTMLPurifier_Generator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Generator.php',
    'HTMLPurifier_HTMLDefinition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLDefinition.php',
    'HTMLPurifier_HTMLModule' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule.php',
    'HTMLPurifier_HTMLModuleManager' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModuleManager.php',
    'HTMLPurifier_HTMLModule_Bdo' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Bdo.php',
    'HTMLPurifier_HTMLModule_CommonAttributes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/CommonAttributes.php',
    'HTMLPurifier_HTMLModule_Edit' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Edit.php',
    'HTMLPurifier_HTMLModule_Forms' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Forms.php',
    'HTMLPurifier_HTMLModule_Hypertext' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Hypertext.php',
    'HTMLPurifier_HTMLModule_Iframe' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Iframe.php',
    'HTMLPurifier_HTMLModule_Image' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Image.php',
    'HTMLPurifier_HTMLModule_Legacy' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Legacy.php',
    'HTMLPurifier_HTMLModule_List' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/List.php',
    'HTMLPurifier_HTMLModule_Name' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Name.php',
    'HTMLPurifier_HTMLModule_Nofollow' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Nofollow.php',
    'HTMLPurifier_HTMLModule_NonXMLCommonAttributes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/NonXMLCommonAttributes.php',
    'HTMLPurifier_HTMLModule_Object' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Object.php',
    'HTMLPurifier_HTMLModule_Presentation' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Presentation.php',
    'HTMLPurifier_HTMLModule_Proprietary' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Proprietary.php',
    'HTMLPurifier_HTMLModule_Ruby' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Ruby.php',
    'HTMLPurifier_HTMLModule_SafeEmbed' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeEmbed.php',
    'HTMLPurifier_HTMLModule_SafeObject' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeObject.php',
    'HTMLPurifier_HTMLModule_SafeScripting' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/SafeScripting.php',
    'HTMLPurifier_HTMLModule_Scripting' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Scripting.php',
    'HTMLPurifier_HTMLModule_StyleAttribute' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/StyleAttribute.php',
    'HTMLPurifier_HTMLModule_Tables' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tables.php',
    'HTMLPurifier_HTMLModule_Target' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Target.php',
    'HTMLPurifier_HTMLModule_TargetBlank' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetBlank.php',
    'HTMLPurifier_HTMLModule_TargetNoopener' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetNoopener.php',
    'HTMLPurifier_HTMLModule_TargetNoreferrer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/TargetNoreferrer.php',
    'HTMLPurifier_HTMLModule_Text' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Text.php',
    'HTMLPurifier_HTMLModule_Tidy' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy.php',
    'HTMLPurifier_HTMLModule_Tidy_Name' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Name.php',
    'HTMLPurifier_HTMLModule_Tidy_Proprietary' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Proprietary.php',
    'HTMLPurifier_HTMLModule_Tidy_Strict' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Strict.php',
    'HTMLPurifier_HTMLModule_Tidy_Transitional' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/Transitional.php',
    'HTMLPurifier_HTMLModule_Tidy_XHTML' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/XHTML.php',
    'HTMLPurifier_HTMLModule_Tidy_XHTMLAndHTML4' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/Tidy/XHTMLAndHTML4.php',
    'HTMLPurifier_HTMLModule_XMLCommonAttributes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/HTMLModule/XMLCommonAttributes.php',
    'HTMLPurifier_IDAccumulator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/IDAccumulator.php',
    'HTMLPurifier_Injector' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector.php',
    'HTMLPurifier_Injector_AutoParagraph' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/AutoParagraph.php',
    'HTMLPurifier_Injector_DisplayLinkURI' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/DisplayLinkURI.php',
    'HTMLPurifier_Injector_Linkify' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/Linkify.php',
    'HTMLPurifier_Injector_PurifierLinkify' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/PurifierLinkify.php',
    'HTMLPurifier_Injector_RemoveEmpty' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/RemoveEmpty.php',
    'HTMLPurifier_Injector_RemoveSpansWithoutAttributes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/RemoveSpansWithoutAttributes.php',
    'HTMLPurifier_Injector_SafeObject' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Injector/SafeObject.php',
    'HTMLPurifier_Language' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Language.php',
    'HTMLPurifier_LanguageFactory' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/LanguageFactory.php',
    'HTMLPurifier_Length' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Length.php',
    'HTMLPurifier_Lexer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Lexer.php',
    'HTMLPurifier_Lexer_DOMLex' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/DOMLex.php',
    'HTMLPurifier_Lexer_DirectLex' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/DirectLex.php',
    'HTMLPurifier_Lexer_PH5P' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Lexer/PH5P.php',
    'HTMLPurifier_Node' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Node.php',
    'HTMLPurifier_Node_Comment' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Node/Comment.php',
    'HTMLPurifier_Node_Element' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Node/Element.php',
    'HTMLPurifier_Node_Text' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Node/Text.php',
    'HTMLPurifier_PercentEncoder' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/PercentEncoder.php',
    'HTMLPurifier_Printer' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Printer.php',
    'HTMLPurifier_Printer_CSSDefinition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Printer/CSSDefinition.php',
    'HTMLPurifier_Printer_ConfigForm' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Printer/ConfigForm.php',
    'HTMLPurifier_Printer_HTMLDefinition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Printer/HTMLDefinition.php',
    'HTMLPurifier_PropertyList' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/PropertyList.php',
    'HTMLPurifier_PropertyListIterator' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/PropertyListIterator.php',
    'HTMLPurifier_Queue' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Queue.php',
    'HTMLPurifier_Strategy' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy.php',
    'HTMLPurifier_Strategy_Composite' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/Composite.php',
    'HTMLPurifier_Strategy_Core' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/Core.php',
    'HTMLPurifier_Strategy_FixNesting' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/FixNesting.php',
    'HTMLPurifier_Strategy_MakeWellFormed' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/MakeWellFormed.php',
    'HTMLPurifier_Strategy_RemoveForeignElements' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/RemoveForeignElements.php',
    'HTMLPurifier_Strategy_ValidateAttributes' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Strategy/ValidateAttributes.php',
    'HTMLPurifier_StringHash' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/StringHash.php',
    'HTMLPurifier_StringHashParser' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/StringHashParser.php',
    'HTMLPurifier_TagTransform' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform.php',
    'HTMLPurifier_TagTransform_Font' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform/Font.php',
    'HTMLPurifier_TagTransform_Simple' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/TagTransform/Simple.php',
    'HTMLPurifier_Token' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token.php',
    'HTMLPurifier_TokenFactory' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/TokenFactory.php',
    'HTMLPurifier_Token_Comment' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/Comment.php',
    'HTMLPurifier_Token_Empty' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/Empty.php',
    'HTMLPurifier_Token_End' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/End.php',
    'HTMLPurifier_Token_Start' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/Start.php',
    'HTMLPurifier_Token_Tag' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/Tag.php',
    'HTMLPurifier_Token_Text' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Token/Text.php',
    'HTMLPurifier_URI' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URI.php',
    'HTMLPurifier_URIDefinition' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIDefinition.php',
    'HTMLPurifier_URIFilter' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter.php',
    'HTMLPurifier_URIFilter_DisableExternal' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableExternal.php',
    'HTMLPurifier_URIFilter_DisableExternalResources' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableExternalResources.php',
    'HTMLPurifier_URIFilter_DisableResources' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/DisableResources.php',
    'HTMLPurifier_URIFilter_HostBlacklist' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/HostBlacklist.php',
    'HTMLPurifier_URIFilter_MakeAbsolute' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/MakeAbsolute.php',
    'HTMLPurifier_URIFilter_Munge' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/Munge.php',
    'HTMLPurifier_URIFilter_SafeIframe' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIFilter/SafeIframe.php',
    'HTMLPurifier_URIParser' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIParser.php',
    'HTMLPurifier_URIScheme' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme.php',
    'HTMLPurifier_URISchemeRegistry' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URISchemeRegistry.php',
    'HTMLPurifier_URIScheme_data' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/data.php',
    'HTMLPurifier_URIScheme_file' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/file.php',
    'HTMLPurifier_URIScheme_ftp' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/ftp.php',
    'HTMLPurifier_URIScheme_http' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/http.php',
    'HTMLPurifier_URIScheme_https' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/https.php',
    'HTMLPurifier_URIScheme_mailto' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/mailto.php',
    'HTMLPurifier_URIScheme_news' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/news.php',
    'HTMLPurifier_URIScheme_nntp' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/nntp.php',
    'HTMLPurifier_URIScheme_tel' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/URIScheme/tel.php',
    'HTMLPurifier_UnitConverter' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/UnitConverter.php',
    'HTMLPurifier_VarParser' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/VarParser.php',
    'HTMLPurifier_VarParserException' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/VarParserException.php',
    'HTMLPurifier_VarParser_Flexible' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/VarParser/Flexible.php',
    'HTMLPurifier_VarParser_Native' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/VarParser/Native.php',
    'HTMLPurifier_Zipper' => $vendorDir . '/ezyang/htmlpurifier/library/HTMLPurifier/Zipper.php',
    'JmesPath\\AstRuntime' => $vendorDir . '/mtdowling/jmespath.php/src/AstRuntime.php',
    'JmesPath\\CompilerRuntime' => $vendorDir . '/mtdowling/jmespath.php/src/CompilerRuntime.php',
    'JmesPath\\DebugRuntime' => $vendorDir . '/mtdowling/jmespath.php/src/DebugRuntime.php',
    'JmesPath\\Env' => $vendorDir . '/mtdowling/jmespath.php/src/Env.php',
    'JmesPath\\FnDispatcher' => $vendorDir . '/mtdowling/jmespath.php/src/FnDispatcher.php',
    'JmesPath\\Lexer' => $vendorDir . '/mtdowling/jmespath.php/src/Lexer.php',
    'JmesPath\\Parser' => $vendorDir . '/mtdowling/jmespath.php/src/Parser.php',
    'JmesPath\\SyntaxErrorException' => $vendorDir . '/mtdowling/jmespath.php/src/SyntaxErrorException.php',
    'JmesPath\\TreeCompiler' => $vendorDir . '/mtdowling/jmespath.php/src/TreeCompiler.php',
    'JmesPath\\TreeInterpreter' => $vendorDir . '/mtdowling/jmespath.php/src/TreeInterpreter.php',
    'JmesPath\\Utils' => $vendorDir . '/mtdowling/jmespath.php/src/Utils.php',
    'Masterminds\\HTML5' => $vendorDir . '/masterminds/html5/src/HTML5.php',
    'Masterminds\\HTML5\\Elements' => $vendorDir . '/masterminds/html5/src/HTML5/Elements.php',
    'Masterminds\\HTML5\\Entities' => $vendorDir . '/masterminds/html5/src/HTML5/Entities.php',
    'Masterminds\\HTML5\\Exception' => $vendorDir . '/masterminds/html5/src/HTML5/Exception.php',
    'Masterminds\\HTML5\\InstructionProcessor' => $vendorDir . '/masterminds/html5/src/HTML5/InstructionProcessor.php',
    'Masterminds\\HTML5\\Parser\\CharacterReference' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/CharacterReference.php',
    'Masterminds\\HTML5\\Parser\\DOMTreeBuilder' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/DOMTreeBuilder.php',
    'Masterminds\\HTML5\\Parser\\EventHandler' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/EventHandler.php',
    'Masterminds\\HTML5\\Parser\\FileInputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/FileInputStream.php',
    'Masterminds\\HTML5\\Parser\\InputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/InputStream.php',
    'Masterminds\\HTML5\\Parser\\ParseError' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/ParseError.php',
    'Masterminds\\HTML5\\Parser\\Scanner' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/Scanner.php',
    'Masterminds\\HTML5\\Parser\\StringInputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/StringInputStream.php',
    'Masterminds\\HTML5\\Parser\\Tokenizer' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/Tokenizer.php',
    'Masterminds\\HTML5\\Parser\\TreeBuildingRules' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/TreeBuildingRules.php',
    'Masterminds\\HTML5\\Parser\\UTF8Utils' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/UTF8Utils.php',
    'Masterminds\\HTML5\\Serializer\\HTML5Entities' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/HTML5Entities.php',
    'Masterminds\\HTML5\\Serializer\\OutputRules' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/OutputRules.php',
    'Masterminds\\HTML5\\Serializer\\RulesInterface' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/RulesInterface.php',
    'Masterminds\\HTML5\\Serializer\\Traverser' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/Traverser.php',
    'Matrix\\Builder' => $vendorDir . '/markbaker/matrix/classes/src/Builder.php',
    'Matrix\\Decomposition\\Decomposition' => $vendorDir . '/markbaker/matrix/classes/src/Decomposition/Decomposition.php',
    'Matrix\\Decomposition\\LU' => $vendorDir . '/markbaker/matrix/classes/src/Decomposition/LU.php',
    'Matrix\\Decomposition\\QR' => $vendorDir . '/markbaker/matrix/classes/src/Decomposition/QR.php',
    'Matrix\\Div0Exception' => $vendorDir . '/markbaker/matrix/classes/src/Div0Exception.php',
    'Matrix\\Exception' => $vendorDir . '/markbaker/matrix/classes/src/Exception.php',
    'Matrix\\Functions' => $vendorDir . '/markbaker/matrix/classes/src/Functions.php',
    'Matrix\\Matrix' => $vendorDir . '/markbaker/matrix/classes/src/Matrix.php',
    'Matrix\\Operations' => $vendorDir . '/markbaker/matrix/classes/src/Operations.php',
    'Matrix\\Operators\\Addition' => $vendorDir . '/markbaker/matrix/classes/src/Operators/Addition.php',
    'Matrix\\Operators\\DirectSum' => $vendorDir . '/markbaker/matrix/classes/src/Operators/DirectSum.php',
    'Matrix\\Operators\\Division' => $vendorDir . '/markbaker/matrix/classes/src/Operators/Division.php',
    'Matrix\\Operators\\Multiplication' => $vendorDir . '/markbaker/matrix/classes/src/Operators/Multiplication.php',
    'Matrix\\Operators\\Operator' => $vendorDir . '/markbaker/matrix/classes/src/Operators/Operator.php',
    'Matrix\\Operators\\Subtraction' => $vendorDir . '/markbaker/matrix/classes/src/Operators/Subtraction.php',
    'MercadoPago\\AdvancedPayments\\AdvancedPayment' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/AdvancedPayments/AdvancedPayment.php',
    'MercadoPago\\AdvancedPayments\\DisbursementRefund' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/AdvancedPayments/DisbursementRefund.php',
    'MercadoPago\\AdvancedPayments\\Refund' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/AdvancedPayments/Refund.php',
    'MercadoPago\\Annotation\\Attribute' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Annotation/Attribute.php',
    'MercadoPago\\Annotation\\DenyDynamicAttribute' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Annotation/DenyDynamicAttribute.php',
    'MercadoPago\\Annotation\\RequestParam' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Annotation/RequestParam.php',
    'MercadoPago\\Annotation\\RestMethod' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Annotation/RestMethod.php',
    'MercadoPago\\AuthorizedPayment' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/AuthorizedPayment.php',
    'MercadoPago\\Card' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Card.php',
    'MercadoPago\\CardToken' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/CardToken.php',
    'MercadoPago\\Chargeback' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Chargeback.php',
    'MercadoPago\\Config' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Config.php',
    'MercadoPago\\Config\\AbstractConfig' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Config/AbstractConfig.php',
    'MercadoPago\\Config\\Json' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Config/Json.php',
    'MercadoPago\\Config\\ParserInterface' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Config/ParserInterface.php',
    'MercadoPago\\Config\\Yaml' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Config/Yaml.php',
    'MercadoPago\\Customer' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Customer.php',
    'MercadoPago\\DiscountCampaign' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/DiscountCampaign.php',
    'MercadoPago\\Documentation' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Documentation.php',
    'MercadoPago\\DummyEntity' => $vendorDir . '/mercadopago/dx-php/tests/DummyEntity.php',
    'MercadoPago\\Entity' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entity.php',
    'MercadoPago\\ErrorCause' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Generic/ErrorCause.php',
    'MercadoPago\\FakeApiHub' => $vendorDir . '/mercadopago/dx-php/tests/FakeApiHub.php',
    'MercadoPago\\Http\\CurlRequest' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Http/CurlRequest.php',
    'MercadoPago\\Http\\HttpRequest' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Http/HttpRequest.php',
    'MercadoPago\\InstoreOrder' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/InstoreOrder.php',
    'MercadoPago\\Invoice' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Invoice.php',
    'MercadoPago\\Item' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Item.php',
    'MercadoPago\\Manager' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Manager.php',
    'MercadoPago\\MerchantOrder' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/MerchantOrder.php',
    'MercadoPago\\MetaDataReader' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/MetaDataReader.php',
    'MercadoPago\\OAuth' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/OAuth.php',
    'MercadoPago\\POS' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/POS.php',
    'MercadoPago\\Payer' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Payer.php',
    'MercadoPago\\Payment' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Payment.php',
    'MercadoPago\\PaymentMethod' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/PaymentMethod.php',
    'MercadoPago\\Plan' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Plan.php',
    'MercadoPago\\Preapproval' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Preapproval.php',
    'MercadoPago\\Preference' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Preference.php',
    'MercadoPago\\RecuperableError' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Generic/RecuperableError.php',
    'MercadoPago\\Refund' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Refund.php',
    'MercadoPago\\RestClient' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/RestClient.php',
    'MercadoPago\\SDK' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/SDK.php',
    'MercadoPago\\SearchResultsArray' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Generic/SearchResultsArray.php',
    'MercadoPago\\Shipments' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shipments.php',
    'MercadoPago\\Subscription' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Subscription.php',
    'MercadoPago\\Tax' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Tax.php',
    'MercadoPago\\Track' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/Track.php',
    'MercadoPago\\TrackValues' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Entities/Shared/TrackValues.php',
    'MercadoPago\\Version' => $vendorDir . '/mercadopago/dx-php/src/MercadoPago/Version.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\ArrayEnabled' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/ArrayEnabled.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\BinaryComparison' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/BinaryComparison.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Calculation' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Calculation.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Category' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Category.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DAverage' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DAverage.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DCount' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DCount.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DCountA' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DCountA.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DGet' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DGet.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DMax' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DMax.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DMin' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DMin.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DProduct' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DProduct.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DStDev' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DStDev.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DStDevP' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DStDevP.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DSum' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DSum.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DVar' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DVar.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DVarP' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DVarP.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Database\\DatabaseAbstract' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Database/DatabaseAbstract.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTime' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTime.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Constants' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Constants.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Current' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Current.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Date' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Date.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\DateParts' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/DateParts.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\DateValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/DateValue.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Days' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Days.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Days360' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Days360.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Difference' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Difference.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Helpers' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Helpers.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Month' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Month.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\NetworkDays' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/NetworkDays.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Time' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Time.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\TimeParts' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/TimeParts.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\TimeValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/TimeValue.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\Week' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/Week.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\WorkDay' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/WorkDay.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\DateTimeExcel\\YearFrac' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/DateTimeExcel/YearFrac.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\ArrayArgumentHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/ArrayArgumentHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\ArrayArgumentProcessor' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/ArrayArgumentProcessor.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\BranchPruner' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/BranchPruner.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\CyclicReferenceStack' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/CyclicReferenceStack.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\FormattedNumber' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/FormattedNumber.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\Logger' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/Logger.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\Operands\\Operand' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/Operands/Operand.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engine\\Operands\\StructuredReference' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engine/Operands/StructuredReference.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\BesselI' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/BesselI.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\BesselJ' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/BesselJ.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\BesselK' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/BesselK.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\BesselY' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/BesselY.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\BitWise' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/BitWise.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\Compare' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/Compare.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\Complex' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/Complex.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ComplexFunctions' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ComplexFunctions.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ComplexOperations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ComplexOperations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\Constants' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/Constants.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertBinary' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertBinary.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertDecimal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertDecimal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertHex' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertHex.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertOctal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertOctal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ConvertUOM' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ConvertUOM.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\EngineeringValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/EngineeringValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\Erf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/Erf.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Engineering\\ErfC' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Engineering/ErfC.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Exception' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Exception.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\ExceptionHandler' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/ExceptionHandler.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Amortization' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Amortization.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\CashFlowValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/CashFlowValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Constant\\Periodic' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Constant\\Periodic\\Cumulative' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Cumulative.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Constant\\Periodic\\Interest' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Interest.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Constant\\Periodic\\InterestAndPrincipal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/InterestAndPrincipal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Constant\\Periodic\\Payments' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Constant/Periodic/Payments.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Single' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Single.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Variable\\NonPeriodic' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Variable/NonPeriodic.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\CashFlow\\Variable\\Periodic' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/CashFlow/Variable/Periodic.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Constants' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Constants.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Coupons' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Coupons.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Depreciation' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Depreciation.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Dollar' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Dollar.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\FinancialValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/FinancialValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Helpers' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Helpers.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\InterestRate' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/InterestRate.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Securities\\AccruedInterest' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Securities/AccruedInterest.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Securities\\Price' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Securities/Price.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Securities\\Rates' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Securities/Rates.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Securities\\SecurityValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Securities/SecurityValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\Securities\\Yields' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/Securities/Yields.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Financial\\TreasuryBill' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Financial/TreasuryBill.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\FormulaParser' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/FormulaParser.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\FormulaToken' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/FormulaToken.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Functions' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Functions.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Information\\ErrorValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Information/ErrorValue.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Information\\ExcelError' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Information/ExcelError.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Information\\Value' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Information/Value.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Internal\\MakeMatrix' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Internal/MakeMatrix.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Internal\\WildcardMatch' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Internal/WildcardMatch.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Logical' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Logical.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Logical\\Boolean' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Logical/Boolean.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Logical\\Conditional' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Logical/Conditional.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Logical\\Operations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Logical/Operations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Address' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Address.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\ExcelMatch' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/ExcelMatch.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Filter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Filter.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Formula' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Formula.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\HLookup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/HLookup.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Helpers' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Helpers.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Hyperlink' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Hyperlink.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Indirect' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Indirect.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Lookup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Lookup.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\LookupBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/LookupBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\LookupRefValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/LookupRefValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Matrix' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Matrix.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Offset' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Offset.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\RowColumnInformation' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/RowColumnInformation.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Selection' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Selection.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Sort' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Sort.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\Unique' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/Unique.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\LookupRef\\VLookup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/LookupRef/VLookup.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Absolute' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Absolute.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Angle' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Angle.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Arabic' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Arabic.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Base' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Base.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Ceiling' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Ceiling.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Combinations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Combinations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Exp' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Exp.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Factorial' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Factorial.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Floor' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Floor.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Gcd' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Gcd.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Helpers' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Helpers.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\IntClass' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/IntClass.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Lcm' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Lcm.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Logarithms' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Logarithms.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\MatrixFunctions' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/MatrixFunctions.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Operations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Operations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Random' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Random.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Roman' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Roman.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Round' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Round.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\SeriesSum' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/SeriesSum.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Sign' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Sign.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Sqrt' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Sqrt.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Subtotal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Subtotal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Sum' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Sum.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\SumSquares' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/SumSquares.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Cosecant' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Cosecant.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Cosine' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Cosine.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Cotangent' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Cotangent.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Secant' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Secant.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Sine' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Sine.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trig\\Tangent' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trig/Tangent.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\MathTrig\\Trunc' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/MathTrig/Trunc.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\AggregateBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/AggregateBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Averages' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Averages.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Averages\\Mean' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Averages/Mean.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Conditional' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Conditional.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Confidence' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Confidence.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Counts' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Counts.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Deviations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Deviations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Beta' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Beta.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Binomial' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Binomial.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\ChiSquared' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/ChiSquared.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\DistributionValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/DistributionValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Exponential' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Exponential.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\F' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/F.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Fisher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Fisher.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Gamma' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Gamma.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\GammaBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/GammaBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\HyperGeometric' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/HyperGeometric.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\LogNormal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/LogNormal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\NewtonRaphson' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/NewtonRaphson.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Normal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Normal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Poisson' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Poisson.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\StandardNormal' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/StandardNormal.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\StudentT' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/StudentT.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Distributions\\Weibull' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Distributions/Weibull.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\MaxMinBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/MaxMinBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Maximum' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Maximum.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Minimum' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Minimum.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Percentiles' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Percentiles.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Permutations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Permutations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Size' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Size.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\StandardDeviations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/StandardDeviations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Standardize' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Standardize.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\StatisticalValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/StatisticalValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Trends' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Trends.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\VarianceBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/VarianceBase.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Statistical\\Variances' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Statistical/Variances.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\CaseConvert' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/CaseConvert.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\CharacterConvert' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/CharacterConvert.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Concatenate' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Concatenate.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Extract' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Extract.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Format' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Format.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Helpers' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Helpers.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Replace' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Replace.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Search' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Search.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Text' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Text.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\TextData\\Trim' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/TextData/Trim.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Token\\Stack' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Token/Stack.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Web' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Web.php',
    'PhpOffice\\PhpSpreadsheet\\Calculation\\Web\\Service' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Calculation/Web/Service.php',
    'PhpOffice\\PhpSpreadsheet\\CellReferenceHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/CellReferenceHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\AddressHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/AddressHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\AddressRange' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/AddressRange.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\AdvancedValueBinder' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/AdvancedValueBinder.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\Cell' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/Cell.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\CellAddress' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/CellAddress.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\CellRange' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/CellRange.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\ColumnRange' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/ColumnRange.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\Coordinate' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/Coordinate.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\DataType' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/DataType.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\DataValidation' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/DataValidation.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\DataValidator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/DataValidator.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\DefaultValueBinder' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/DefaultValueBinder.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\Hyperlink' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/Hyperlink.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\IValueBinder' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/IValueBinder.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\IgnoredErrors' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/IgnoredErrors.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\RowRange' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/RowRange.php',
    'PhpOffice\\PhpSpreadsheet\\Cell\\StringValueBinder' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Cell/StringValueBinder.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Axis' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Axis.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\AxisText' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/AxisText.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Chart' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Chart.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\ChartColor' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/ChartColor.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\DataSeries' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/DataSeries.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\DataSeriesValues' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/DataSeriesValues.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Exception' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Exception.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\GridLines' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/GridLines.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Layout' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Layout.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Legend' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Legend.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\PlotArea' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/PlotArea.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Renderer\\IRenderer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Renderer/IRenderer.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Renderer\\JpGraph' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Renderer/JpGraph.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Renderer\\JpGraphRendererBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Renderer/JpGraphRendererBase.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Renderer\\MtJpGraphRenderer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Renderer/MtJpGraphRenderer.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\Title' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/Title.php',
    'PhpOffice\\PhpSpreadsheet\\Chart\\TrendLine' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Chart/TrendLine.php',
    'PhpOffice\\PhpSpreadsheet\\Collection\\Cells' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Collection/Cells.php',
    'PhpOffice\\PhpSpreadsheet\\Collection\\CellsFactory' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Collection/CellsFactory.php',
    'PhpOffice\\PhpSpreadsheet\\Collection\\Memory\\SimpleCache1' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Collection/Memory/SimpleCache1.php',
    'PhpOffice\\PhpSpreadsheet\\Collection\\Memory\\SimpleCache3' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Collection/Memory/SimpleCache3.php',
    'PhpOffice\\PhpSpreadsheet\\Comment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Comment.php',
    'PhpOffice\\PhpSpreadsheet\\DefinedName' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/DefinedName.php',
    'PhpOffice\\PhpSpreadsheet\\Document\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Document/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Document\\Security' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Document/Security.php',
    'PhpOffice\\PhpSpreadsheet\\Exception' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Exception.php',
    'PhpOffice\\PhpSpreadsheet\\HashTable' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/HashTable.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Dimension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Dimension.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Downloader' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Downloader.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Handler' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Handler.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Html' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Html.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Sample' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Sample.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\Size' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/Size.php',
    'PhpOffice\\PhpSpreadsheet\\Helper\\TextGrid' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Helper/TextGrid.php',
    'PhpOffice\\PhpSpreadsheet\\IComparable' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/IComparable.php',
    'PhpOffice\\PhpSpreadsheet\\IOFactory' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/IOFactory.php',
    'PhpOffice\\PhpSpreadsheet\\NamedFormula' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/NamedFormula.php',
    'PhpOffice\\PhpSpreadsheet\\NamedRange' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/NamedRange.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\BaseReader' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/BaseReader.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Csv' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Csv.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Csv\\Delimiter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Csv/Delimiter.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\DefaultReadFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/DefaultReadFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Exception' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Exception.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Gnumeric' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Gnumeric.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Gnumeric\\PageSetup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Gnumeric/PageSetup.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Gnumeric\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Gnumeric/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Gnumeric\\Styles' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Gnumeric/Styles.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Html' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Html.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\IReadFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/IReadFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\IReader' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/IReader.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\AutoFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/AutoFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\BaseLoader' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/BaseLoader.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\DefinedNames' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/DefinedNames.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\FormulaTranslator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/FormulaTranslator.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\PageSettings' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/PageSettings.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Ods\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Ods/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Security\\XmlScanner' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Security/XmlScanner.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Slk' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Slk.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Color' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Color.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Color\\BIFF5' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Color/BIFF5.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Color\\BIFF8' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Color/BIFF8.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Color\\BuiltIn' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Color/BuiltIn.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\ConditionalFormatting' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/ConditionalFormatting.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\DataValidationHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/DataValidationHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\ErrorCode' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/ErrorCode.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Escher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Escher.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\MD5' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/MD5.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\RC4' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/RC4.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Style\\Border' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Style/Border.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Style\\CellAlignment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Style/CellAlignment.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Style\\CellFont' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Style/CellFont.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xls\\Style\\FillPattern' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xls/Style/FillPattern.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\AutoFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/AutoFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\BaseParserClass' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/BaseParserClass.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Chart' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Chart.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\ColumnAndRowAttributes' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/ColumnAndRowAttributes.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\ConditionalStyles' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/ConditionalStyles.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\DataValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/DataValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Hyperlinks' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Hyperlinks.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Namespaces' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Namespaces.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\PageSetup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/PageSetup.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\SharedFormula' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/SharedFormula.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\SheetViewOptions' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/SheetViewOptions.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\SheetViews' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/SheetViews.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Styles' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Styles.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\TableReader' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/TableReader.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\Theme' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/Theme.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xlsx\\WorkbookView' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xlsx/WorkbookView.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\DataValidations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/DataValidations.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\PageSettings' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/PageSettings.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Properties' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Properties.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\Alignment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/Alignment.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\Border' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/Border.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\Fill' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/Fill.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\Font' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/Font.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\NumberFormat' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/NumberFormat.php',
    'PhpOffice\\PhpSpreadsheet\\Reader\\Xml\\Style\\StyleBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Reader/Xml/Style/StyleBase.php',
    'PhpOffice\\PhpSpreadsheet\\ReferenceHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/ReferenceHelper.php',
    'PhpOffice\\PhpSpreadsheet\\RichText\\ITextElement' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/RichText/ITextElement.php',
    'PhpOffice\\PhpSpreadsheet\\RichText\\RichText' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/RichText/RichText.php',
    'PhpOffice\\PhpSpreadsheet\\RichText\\Run' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/RichText/Run.php',
    'PhpOffice\\PhpSpreadsheet\\RichText\\TextElement' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/RichText/TextElement.php',
    'PhpOffice\\PhpSpreadsheet\\Settings' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Settings.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\CodePage' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/CodePage.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Date' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Date.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Drawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Drawing.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DgContainer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DgContainer.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DgContainer\\SpgrContainer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DgContainer/SpgrContainer.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DgContainer\\SpgrContainer\\SpContainer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DgContainer/SpgrContainer/SpContainer.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DggContainer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DggContainer.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DggContainer\\BstoreContainer' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DggContainer/BstoreContainer.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DggContainer\\BstoreContainer\\BSE' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DggContainer/BstoreContainer/BSE.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Escher\\DggContainer\\BstoreContainer\\BSE\\Blip' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Escher/DggContainer/BstoreContainer/BSE/Blip.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\File' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/File.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Font' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Font.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\IntOrFloat' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/IntOrFloat.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLE' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLE.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLERead' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLERead.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLE\\ChainedBlockStream' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLE/ChainedBlockStream.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLE\\PPS' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLE/PPS.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLE\\PPS\\File' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLE/PPS/File.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\OLE\\PPS\\Root' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/OLE/PPS/Root.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\PasswordHasher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/PasswordHasher.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\StringHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/StringHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\TimeZone' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/TimeZone.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\BestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/BestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\ExponentialBestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/ExponentialBestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\LinearBestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/LinearBestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\LogarithmicBestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/LogarithmicBestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\PolynomialBestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/PolynomialBestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\PowerBestFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/PowerBestFit.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Trend\\Trend' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Trend/Trend.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\XMLWriter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/XMLWriter.php',
    'PhpOffice\\PhpSpreadsheet\\Shared\\Xls' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Shared/Xls.php',
    'PhpOffice\\PhpSpreadsheet\\Spreadsheet' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Spreadsheet.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Alignment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Alignment.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Border' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Border.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Borders' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Borders.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Color' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Color.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Conditional' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Conditional.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\CellMatcher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/CellMatcher.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\CellStyleAssessor' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/CellStyleAssessor.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\ConditionalDataBar' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBar.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\ConditionalDataBarExtension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalDataBarExtension.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\ConditionalFormatValueObject' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormatValueObject.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\ConditionalFormattingRuleExtension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/ConditionalFormattingRuleExtension.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\StyleMerger' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/StyleMerger.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\Blanks' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/Blanks.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\CellValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/CellValue.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\DateValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/DateValue.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\Duplicates' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/Duplicates.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\Errors' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/Errors.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\Expression' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/Expression.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\TextValue' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/TextValue.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\WizardAbstract' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/WizardAbstract.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\ConditionalFormatting\\Wizard\\WizardInterface' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/ConditionalFormatting/Wizard/WizardInterface.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Fill' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Fill.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Font' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Font.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\BaseFormatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/BaseFormatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\DateFormatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/DateFormatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Formatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Formatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\FractionFormatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/FractionFormatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\NumberFormatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/NumberFormatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\PercentageFormatter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/PercentageFormatter.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Accounting' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Accounting.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Currency' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Currency.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Date' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Date.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\DateTime' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/DateTime.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\DateTimeWizard' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/DateTimeWizard.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Duration' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Duration.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Locale' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Locale.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Number' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Number.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\NumberBase' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/NumberBase.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Percentage' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Percentage.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Scientific' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Scientific.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Time' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Time.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\NumberFormat\\Wizard\\Wizard' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/NumberFormat/Wizard/Wizard.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Protection' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Protection.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\RgbTint' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/RgbTint.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Style' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Style.php',
    'PhpOffice\\PhpSpreadsheet\\Style\\Supervisor' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Style/Supervisor.php',
    'PhpOffice\\PhpSpreadsheet\\Theme' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Theme.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\AutoFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/AutoFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\AutoFilter\\Column' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/AutoFilter/Column.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\AutoFilter\\Column\\Rule' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/AutoFilter/Column/Rule.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\AutoFit' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/AutoFit.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\BaseDrawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/BaseDrawing.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\CellIterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/CellIterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Column' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Column.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\ColumnCellIterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/ColumnCellIterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\ColumnDimension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/ColumnDimension.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\ColumnIterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/ColumnIterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Dimension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Dimension.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Drawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Drawing.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Drawing\\Shadow' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Drawing/Shadow.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\HeaderFooter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/HeaderFooter.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\HeaderFooterDrawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/HeaderFooterDrawing.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Iterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Iterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\MemoryDrawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/MemoryDrawing.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\PageBreak' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/PageBreak.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\PageMargins' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/PageMargins.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\PageSetup' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/PageSetup.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Protection' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Protection.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Row' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Row.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\RowCellIterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/RowCellIterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\RowDimension' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/RowDimension.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\RowIterator' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/RowIterator.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\SheetView' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/SheetView.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Table' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Table.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Table\\Column' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Table/Column.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Table\\TableStyle' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Table/TableStyle.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Validations' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Validations.php',
    'PhpOffice\\PhpSpreadsheet\\Worksheet\\Worksheet' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Worksheet/Worksheet.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\BaseWriter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/BaseWriter.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Csv' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Csv.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Exception' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Exception.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Html' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Html.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\IWriter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/IWriter.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\AutoFilters' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/AutoFilters.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Cell\\Comment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Cell/Comment.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Cell\\Style' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Cell/Style.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Content' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Content.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Formula' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Formula.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Meta' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Meta.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\MetaInf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/MetaInf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Mimetype' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Mimetype.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\NamedExpressions' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/NamedExpressions.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Settings' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Settings.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Styles' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Styles.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\Thumbnails' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/Thumbnails.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Ods\\WriterPart' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Ods/WriterPart.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Pdf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Pdf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Pdf\\Dompdf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Pdf/Dompdf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Pdf\\Mpdf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Pdf/Mpdf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Pdf\\Tcpdf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Pdf/Tcpdf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\BIFFwriter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/BIFFwriter.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\CellDataValidation' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/CellDataValidation.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\ConditionalHelper' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/ConditionalHelper.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\ErrorCode' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/ErrorCode.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Escher' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Escher.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Font' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Font.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Parser' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Parser.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Style\\CellAlignment' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Style/CellAlignment.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Style\\CellBorder' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Style/CellBorder.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Style\\CellFill' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Style/CellFill.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Style\\ColorMap' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Style/ColorMap.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Workbook' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Workbook.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Worksheet' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Worksheet.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xls\\Xf' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xls/Xf.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\AutoFilter' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/AutoFilter.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Chart' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Chart.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Comments' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Comments.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\ContentTypes' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/ContentTypes.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\DefinedNames' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/DefinedNames.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\DocProps' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/DocProps.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Drawing' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Drawing.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\FunctionPrefix' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/FunctionPrefix.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Rels' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Rels.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\RelsRibbon' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/RelsRibbon.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\RelsVBA' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/RelsVBA.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\StringTable' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/StringTable.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Style' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Style.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Table' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Table.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Theme' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Theme.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Workbook' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Workbook.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\Worksheet' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/Worksheet.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\Xlsx\\WriterPart' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/Xlsx/WriterPart.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\ZipStream0' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/ZipStream0.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\ZipStream2' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/ZipStream2.php',
    'PhpOffice\\PhpSpreadsheet\\Writer\\ZipStream3' => $vendorDir . '/phpoffice/phpspreadsheet/src/PhpSpreadsheet/Writer/ZipStream3.php',
    'Psr\\Cache\\CacheException' => $vendorDir . '/psr/cache/src/CacheException.php',
    'Psr\\Cache\\CacheItemInterface' => $vendorDir . '/psr/cache/src/CacheItemInterface.php',
    'Psr\\Cache\\CacheItemPoolInterface' => $vendorDir . '/psr/cache/src/CacheItemPoolInterface.php',
    'Psr\\Cache\\InvalidArgumentException' => $vendorDir . '/psr/cache/src/InvalidArgumentException.php',
    'Psr\\Http\\Client\\ClientExceptionInterface' => $vendorDir . '/psr/http-client/src/ClientExceptionInterface.php',
    'Psr\\Http\\Client\\ClientInterface' => $vendorDir . '/psr/http-client/src/ClientInterface.php',
    'Psr\\Http\\Client\\NetworkExceptionInterface' => $vendorDir . '/psr/http-client/src/NetworkExceptionInterface.php',
    'Psr\\Http\\Client\\RequestExceptionInterface' => $vendorDir . '/psr/http-client/src/RequestExceptionInterface.php',
    'Psr\\Http\\Message\\MessageInterface' => $vendorDir . '/psr/http-message/src/MessageInterface.php',
    'Psr\\Http\\Message\\RequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/RequestFactoryInterface.php',
    'Psr\\Http\\Message\\RequestInterface' => $vendorDir . '/psr/http-message/src/RequestInterface.php',
    'Psr\\Http\\Message\\ResponseFactoryInterface' => $vendorDir . '/psr/http-factory/src/ResponseFactoryInterface.php',
    'Psr\\Http\\Message\\ResponseInterface' => $vendorDir . '/psr/http-message/src/ResponseInterface.php',
    'Psr\\Http\\Message\\ServerRequestFactoryInterface' => $vendorDir . '/psr/http-factory/src/ServerRequestFactoryInterface.php',
    'Psr\\Http\\Message\\ServerRequestInterface' => $vendorDir . '/psr/http-message/src/ServerRequestInterface.php',
    'Psr\\Http\\Message\\StreamFactoryInterface' => $vendorDir . '/psr/http-factory/src/StreamFactoryInterface.php',
    'Psr\\Http\\Message\\StreamInterface' => $vendorDir . '/psr/http-message/src/StreamInterface.php',
    'Psr\\Http\\Message\\UploadedFileFactoryInterface' => $vendorDir . '/psr/http-factory/src/UploadedFileFactoryInterface.php',
    'Psr\\Http\\Message\\UploadedFileInterface' => $vendorDir . '/psr/http-message/src/UploadedFileInterface.php',
    'Psr\\Http\\Message\\UriFactoryInterface' => $vendorDir . '/psr/http-factory/src/UriFactoryInterface.php',
    'Psr\\Http\\Message\\UriInterface' => $vendorDir . '/psr/http-message/src/UriInterface.php',
    'Psr\\SimpleCache\\CacheException' => $vendorDir . '/psr/simple-cache/src/CacheException.php',
    'Psr\\SimpleCache\\CacheInterface' => $vendorDir . '/psr/simple-cache/src/CacheInterface.php',
    'Psr\\SimpleCache\\InvalidArgumentException' => $vendorDir . '/psr/simple-cache/src/InvalidArgumentException.php',
    'Sabberworm\\CSS\\CSSElement' => $vendorDir . '/sabberworm/php-css-parser/src/CSSElement.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/AtRuleBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSList.php',
    'Sabberworm\\CSS\\CSSList\\Document' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/Document.php',
    'Sabberworm\\CSS\\CSSList\\KeyFrame' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/KeyFrame.php',
    'Sabberworm\\CSS\\Comment\\Comment' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Comment.php',
    'Sabberworm\\CSS\\Comment\\Commentable' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Commentable.php',
    'Sabberworm\\CSS\\OutputFormat' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormat.php',
    'Sabberworm\\CSS\\OutputFormatter' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormatter.php',
    'Sabberworm\\CSS\\Parser' => $vendorDir . '/sabberworm/php-css-parser/src/Parser.php',
    'Sabberworm\\CSS\\Parsing\\Anchor' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/Anchor.php',
    'Sabberworm\\CSS\\Parsing\\OutputException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/OutputException.php',
    'Sabberworm\\CSS\\Parsing\\ParserState' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/ParserState.php',
    'Sabberworm\\CSS\\Parsing\\SourceException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/SourceException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedEOFException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedEOFException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedTokenException.php',
    'Sabberworm\\CSS\\Position\\Position' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Position.php',
    'Sabberworm\\CSS\\Position\\Positionable' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Positionable.php',
    'Sabberworm\\CSS\\Property\\AtRule' => $vendorDir . '/sabberworm/php-css-parser/src/Property/AtRule.php',
    'Sabberworm\\CSS\\Property\\CSSNamespace' => $vendorDir . '/sabberworm/php-css-parser/src/Property/CSSNamespace.php',
    'Sabberworm\\CSS\\Property\\Charset' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Charset.php',
    'Sabberworm\\CSS\\Property\\Import' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Import.php',
    'Sabberworm\\CSS\\Property\\KeyframeSelector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/KeyframeSelector.php',
    'Sabberworm\\CSS\\Property\\Selector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Selector.php',
    'Sabberworm\\CSS\\Renderable' => $vendorDir . '/sabberworm/php-css-parser/src/Renderable.php',
    'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/AtRuleSet.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/DeclarationBlock.php',
    'Sabberworm\\CSS\\RuleSet\\RuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/RuleSet.php',
    'Sabberworm\\CSS\\Rule\\Rule' => $vendorDir . '/sabberworm/php-css-parser/src/Rule/Rule.php',
    'Sabberworm\\CSS\\Settings' => $vendorDir . '/sabberworm/php-css-parser/src/Settings.php',
    'Sabberworm\\CSS\\Value\\CSSFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSFunction.php',
    'Sabberworm\\CSS\\Value\\CSSString' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSString.php',
    'Sabberworm\\CSS\\Value\\CalcFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcFunction.php',
    'Sabberworm\\CSS\\Value\\CalcRuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcRuleValueList.php',
    'Sabberworm\\CSS\\Value\\Color' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Color.php',
    'Sabberworm\\CSS\\Value\\LineName' => $vendorDir . '/sabberworm/php-css-parser/src/Value/LineName.php',
    'Sabberworm\\CSS\\Value\\PrimitiveValue' => $vendorDir . '/sabberworm/php-css-parser/src/Value/PrimitiveValue.php',
    'Sabberworm\\CSS\\Value\\RuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/RuleValueList.php',
    'Sabberworm\\CSS\\Value\\Size' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Size.php',
    'Sabberworm\\CSS\\Value\\URL' => $vendorDir . '/sabberworm/php-css-parser/src/Value/URL.php',
    'Sabberworm\\CSS\\Value\\Value' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Value.php',
    'Sabberworm\\CSS\\Value\\ValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/ValueList.php',
    'Svg\\CssLength' => $vendorDir . '/phenx/php-svg-lib/src/Svg/CssLength.php',
    'Svg\\DefaultStyle' => $vendorDir . '/phenx/php-svg-lib/src/Svg/DefaultStyle.php',
    'Svg\\Document' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Document.php',
    'Svg\\Gradient\\Stop' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Gradient/Stop.php',
    'Svg\\Style' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Style.php',
    'Svg\\Surface\\CPdf' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/CPdf.php',
    'Svg\\Surface\\SurfaceCpdf' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfaceCpdf.php',
    'Svg\\Surface\\SurfaceInterface' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfaceInterface.php',
    'Svg\\Surface\\SurfacePDFLib' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfacePDFLib.php',
    'Svg\\Tag\\AbstractTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/AbstractTag.php',
    'Svg\\Tag\\Anchor' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Anchor.php',
    'Svg\\Tag\\Circle' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Circle.php',
    'Svg\\Tag\\ClipPath' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/ClipPath.php',
    'Svg\\Tag\\Ellipse' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Ellipse.php',
    'Svg\\Tag\\Group' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Group.php',
    'Svg\\Tag\\Image' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Image.php',
    'Svg\\Tag\\Line' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Line.php',
    'Svg\\Tag\\LinearGradient' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/LinearGradient.php',
    'Svg\\Tag\\Path' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Path.php',
    'Svg\\Tag\\Polygon' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Polygon.php',
    'Svg\\Tag\\Polyline' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Polyline.php',
    'Svg\\Tag\\RadialGradient' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/RadialGradient.php',
    'Svg\\Tag\\Rect' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Rect.php',
    'Svg\\Tag\\Shape' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Shape.php',
    'Svg\\Tag\\Stop' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Stop.php',
    'Svg\\Tag\\StyleTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/StyleTag.php',
    'Svg\\Tag\\Symbol' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Symbol.php',
    'Svg\\Tag\\Text' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Text.php',
    'Svg\\Tag\\UseTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/UseTag.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'ZipStream\\CentralDirectoryFileHeader' => $vendorDir . '/maennchen/zipstream-php/src/CentralDirectoryFileHeader.php',
    'ZipStream\\CompressionMethod' => $vendorDir . '/maennchen/zipstream-php/src/CompressionMethod.php',
    'ZipStream\\DataDescriptor' => $vendorDir . '/maennchen/zipstream-php/src/DataDescriptor.php',
    'ZipStream\\EndOfCentralDirectory' => $vendorDir . '/maennchen/zipstream-php/src/EndOfCentralDirectory.php',
    'ZipStream\\Exception' => $vendorDir . '/maennchen/zipstream-php/src/Exception.php',
    'ZipStream\\Exception\\DosTimeOverflowException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/DosTimeOverflowException.php',
    'ZipStream\\Exception\\FileNotFoundException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/FileNotFoundException.php',
    'ZipStream\\Exception\\FileNotReadableException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/FileNotReadableException.php',
    'ZipStream\\Exception\\FileSizeIncorrectException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/FileSizeIncorrectException.php',
    'ZipStream\\Exception\\OverflowException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/OverflowException.php',
    'ZipStream\\Exception\\ResourceActionException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/ResourceActionException.php',
    'ZipStream\\Exception\\SimulationFileUnknownException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/SimulationFileUnknownException.php',
    'ZipStream\\Exception\\StreamNotReadableException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/StreamNotReadableException.php',
    'ZipStream\\Exception\\StreamNotSeekableException' => $vendorDir . '/maennchen/zipstream-php/src/Exception/StreamNotSeekableException.php',
    'ZipStream\\File' => $vendorDir . '/maennchen/zipstream-php/src/File.php',
    'ZipStream\\GeneralPurposeBitFlag' => $vendorDir . '/maennchen/zipstream-php/src/GeneralPurposeBitFlag.php',
    'ZipStream\\LocalFileHeader' => $vendorDir . '/maennchen/zipstream-php/src/LocalFileHeader.php',
    'ZipStream\\OperationMode' => $vendorDir . '/maennchen/zipstream-php/src/OperationMode.php',
    'ZipStream\\PackField' => $vendorDir . '/maennchen/zipstream-php/src/PackField.php',
    'ZipStream\\Stream\\CallbackStreamWrapper' => $vendorDir . '/maennchen/zipstream-php/src/Stream/CallbackStreamWrapper.php',
    'ZipStream\\Time' => $vendorDir . '/maennchen/zipstream-php/src/Time.php',
    'ZipStream\\Version' => $vendorDir . '/maennchen/zipstream-php/src/Version.php',
    'ZipStream\\Zip64\\DataDescriptor' => $vendorDir . '/maennchen/zipstream-php/src/Zip64/DataDescriptor.php',
    'ZipStream\\Zip64\\EndOfCentralDirectory' => $vendorDir . '/maennchen/zipstream-php/src/Zip64/EndOfCentralDirectory.php',
    'ZipStream\\Zip64\\EndOfCentralDirectoryLocator' => $vendorDir . '/maennchen/zipstream-php/src/Zip64/EndOfCentralDirectoryLocator.php',
    'ZipStream\\Zip64\\ExtendedInformationExtraField' => $vendorDir . '/maennchen/zipstream-php/src/Zip64/ExtendedInformationExtraField.php',
    'ZipStream\\ZipStream' => $vendorDir . '/maennchen/zipstream-php/src/ZipStream.php',
    'ZipStream\\Zs\\ExtendedInformationExtraField' => $vendorDir . '/maennchen/zipstream-php/src/Zs/ExtendedInformationExtraField.php',
);
